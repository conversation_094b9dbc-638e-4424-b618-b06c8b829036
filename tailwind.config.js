/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      animation: {
        'shimmer': 'shimmer 2s infinite linear',
        'float': 'float 3s ease-in-out infinite',
        'slide-in-up': 'slideInUp 0.5s ease-out',
        'slide-in-left': 'slideInLeft 0.5s ease-out',
        'slide-in-right': 'slideInRight 0.5s ease-out',
        'scale-in': 'scaleIn 0.3s ease-out',
        'bounce-in': 'bounceIn 0.6s ease-out',
        'fade-in-up': 'fadeInUp 0.4s ease-out',
        'pulse-glow': 'pulse-glow 2s infinite',
      },
      keyframes: {
        shimmer: {
          '0%': { backgroundPosition: '-200% 0' },
          '100%': { backgroundPosition: '200% 0' },
        },
        float: {
          '0%, 100%': { transform: 'translateY(0px)' },
          '50%': { transform: 'translateY(-10px)' },
        },
        slideInUp: {
          from: {
            opacity: '0',
            transform: 'translateY(30px)',
          },
          to: {
            opacity: '1',
            transform: 'translateY(0)',
          },
        },
        slideInLeft: {
          from: {
            opacity: '0',
            transform: 'translateX(-30px)',
          },
          to: {
            opacity: '1',
            transform: 'translateX(0)',
          },
        },
        slideInRight: {
          from: {
            opacity: '0',
            transform: 'translateX(30px)',
          },
          to: {
            opacity: '1',
            transform: 'translateX(0)',
          },
        },
        scaleIn: {
          from: {
            opacity: '0',
            transform: 'scale(0.9)',
          },
          to: {
            opacity: '1',
            transform: 'scale(1)',
          },
        },
        bounceIn: {
          '0%': {
            opacity: '0',
            transform: 'scale(0.3)',
          },
          '50%': {
            opacity: '1',
            transform: 'scale(1.05)',
          },
          '70%': {
            transform: 'scale(0.9)',
          },
          '100%': {
            opacity: '1',
            transform: 'scale(1)',
          },
        },
        fadeInUp: {
          from: {
            opacity: '0',
            transform: 'translateY(20px)',
          },
          to: {
            opacity: '1',
            transform: 'translateY(0)',
          },
        },
        'pulse-glow': {
          '0%, 100%': {
            boxShadow: '0 0 0 0 rgba(228, 113, 74, 0.4)',
          },
          '50%': {
            boxShadow: '0 0 0 10px rgba(228, 113, 74, 0)',
          },
        },
      },
      transitionTimingFunction: {
        'bounce': 'cubic-bezier(0.68, -0.55, 0.265, 1.55)',
        'spring': 'cubic-bezier(0.175, 0.885, 0.32, 1.275)',
      },
      colors: {
        // Elegant Neutral Palette - Warm Beiges/Creams with Rose Gold
        'primary': {
          50: '#fdfcf8',   // Lightest cream
          100: '#faf7f0',  // Very light cream
          200: '#f5f0e8',  // Light cream
          300: '#ede4d3',  // Soft beige
          400: '#e0d0b7',  // Warm beige
          500: '#d4b896',  // Medium beige
          600: '#c49a6b',  // Darker beige
          700: '#b08355',  // Deep beige
          800: '#8a6b44',  // Rich beige
          900: '#6b5137',  // Darkest beige
        },
        // Rose Gold Accents
        'accent': {
          50: '#fef8f6',   // Lightest rose gold
          100: '#fdeee8',  // Very light rose gold
          200: '#fad7c4',  // Light rose gold
          300: '#f7b896',  // Soft rose gold
          400: '#f19066',  // Medium rose gold
          500: '#e8714a',  // Rose gold primary
          600: '#d4562f',  // Deep rose gold
          700: '#b5421f',  // Darker rose gold
          800: '#93361a',  // Rich rose gold
          900: '#732c18',  // Darkest rose gold
        },
        // Complementary Neutrals
        'secondary': {
          50: '#fafafa',   // Almost white
          100: '#f5f5f5',  // Light gray
          200: '#e5e5e5',  // Soft gray
          300: '#d4d4d4',  // Medium gray
          400: '#a3a3a3',  // Gray
          500: '#737373',  // Dark gray
          600: '#525252',  // Darker gray
          700: '#404040',  // Deep gray
          800: '#262626',  // Very dark gray
          900: '#171717',  // Almost black
        },
        // Keep existing pink and purple for backward compatibility
        'pink': {
          50: '#fef7f0',
          100: '#feecdc',
          200: '#fcd9bd',
          300: '#fdba8c',
          400: '#ff8a65',
          500: '#f56565',
          600: '#e53e3e',
          700: '#c53030',
          800: '#9c2626',
          900: '#822727',
        },
        'purple': {
          50: '#f8f7ff',
          100: '#f2f0ff',
          200: '#e8e4ff',
          300: '#d6ccff',
          400: '#bda7ff',
          500: '#9f78ff',
          600: '#8b4df7',
          700: '#7b35e3',
          800: '#672bbf',
          900: '#56269c',
        }
      }
    },
  },
  plugins: [],
}
