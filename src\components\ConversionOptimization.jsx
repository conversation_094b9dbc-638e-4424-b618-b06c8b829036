import React, { useState, useEffect } from 'react';
import { X, Clock, Users, Zap, Gift, AlertCircle, Star, TrendingUp } from 'lucide-react';

// Urgency Indicators Component
export const UrgencyIndicators = ({ product, className = "" }) => {
  const [stockLevel, setStockLevel] = useState(null);
  const [recentPurchases, setRecentPurchases] = useState([]);
  const [timeLeft, setTimeLeft] = useState(null);

  useEffect(() => {
    // Simulate stock level (random between 1-10)
    setStockLevel(Math.floor(Math.random() * 10) + 1);
    
    // Simulate recent purchases
    const purchases = [
      { name: "Aisha K.", time: "2 minutes ago", location: "Karachi" },
      { name: "Fatima S.", time: "5 minutes ago", location: "Lahore" },
      { name: "Zara M.", time: "8 minutes ago", location: "Islamabad" },
      { name: "<PERSON>sha R.", time: "12 minutes ago", location: "Faisalabad" }
    ];
    setRecentPurchases(purchases.slice(0, Math.floor(Math.random() * 3) + 1));
    
    // Simulate sale end time (random between 1-24 hours)
    if (product.isOnSale) {
      const hoursLeft = Math.floor(Math.random() * 24) + 1;
      setTimeLeft(hoursLeft * 60 * 60 * 1000); // Convert to milliseconds
    }
  }, [product]);

  const [countdown, setCountdown] = useState("");

  useEffect(() => {
    if (timeLeft) {
      const timer = setInterval(() => {
        const now = new Date().getTime();
        const end = new Date(now + timeLeft).getTime();
        const distance = end - now;
        
        if (distance > 0) {
          const hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
          const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
          const seconds = Math.floor((distance % (1000 * 60)) / 1000);
          
          setCountdown(`${hours}h ${minutes}m ${seconds}s`);
        } else {
          setCountdown("EXPIRED");
        }
      }, 1000);
      
      return () => clearInterval(timer);
    }
  }, [timeLeft]);

  return (
    <div className={`space-y-3 ${className}`}>
      {/* Stock Level Indicator */}
      {stockLevel && stockLevel <= 5 && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-3 flex items-center gap-2">
          <AlertCircle className="w-5 h-5 text-red-500" />
          <span className="text-sm text-red-700">
            <span className="font-semibold">Only {stockLevel} left</span> in stock - order soon!
          </span>
        </div>
      )}

      {/* Recent Purchases */}
      {recentPurchases.length > 0 && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
          <div className="flex items-center gap-2 mb-2">
            <Users className="w-5 h-5 text-blue-500" />
            <span className="text-sm font-semibold text-blue-800">Recent Activity</span>
          </div>
          <div className="space-y-1">
            {recentPurchases.map((purchase, index) => (
              <div key={index} className="text-sm text-blue-700">
                <span className="font-medium">{purchase.name}</span> from {purchase.location} bought this {purchase.time}
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Sale Countdown */}
      {product.isOnSale && countdown && countdown !== "EXPIRED" && (
        <div className="bg-orange-50 border border-orange-200 rounded-lg p-3 flex items-center gap-2">
          <Clock className="w-5 h-5 text-orange-500" />
          <span className="text-sm text-orange-700">
            <span className="font-semibold">Sale ends in: {countdown}</span>
          </span>
        </div>
      )}

      {/* Trending Badge */}
      {product.rating && product.rating > 4.5 && (
        <div className="bg-green-50 border border-green-200 rounded-lg p-3 flex items-center gap-2">
          <TrendingUp className="w-5 h-5 text-green-500" />
          <span className="text-sm text-green-700">
            <span className="font-semibold">Trending!</span> High demand item - {product.reviews} customers love this
          </span>
        </div>
      )}
    </div>
  );
};

// Exit Intent Popup Component
export const ExitIntentPopup = ({ isOpen, onClose, onSubscribe }) => {
  const [email, setEmail] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);
    
    try {
      await onSubscribe(email);
      onClose();
    } catch (error) {
      console.error('Subscription error:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4 backdrop-blur-sm">
      <div className="bg-white rounded-2xl shadow-2xl max-w-md w-full p-6 relative">
        <button 
          onClick={onClose}
          className="absolute top-4 right-4 p-2 hover:bg-gray-100 rounded-full transition-colors"
        >
          <X className="w-5 h-5 text-gray-500" />
        </button>

        <div className="text-center">
          <div className="w-16 h-16 bg-gradient-to-r from-accent-500 to-accent-600 rounded-full flex items-center justify-center mx-auto mb-4">
            <Gift className="w-8 h-8 text-white" />
          </div>
          
          <h2 className="text-2xl font-bold text-gray-800 mb-2">
            Wait! Don't Miss Out!
          </h2>
          
          <p className="text-gray-600 mb-6">
            Get <span className="font-semibold text-accent-600">15% OFF</span> your first order + exclusive access to new arrivals
          </p>

          <form onSubmit={handleSubmit} className="space-y-4">
            <input
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              placeholder="Enter your email address"
              required
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-accent-500 focus:border-transparent"
            />
            
            <button
              type="submit"
              disabled={isSubmitting}
              className="w-full bg-gradient-to-r from-accent-500 to-accent-600 text-white py-3 px-6 rounded-lg hover:from-accent-600 hover:to-accent-700 transition-all duration-200 font-semibold disabled:opacity-50"
            >
              {isSubmitting ? 'Subscribing...' : 'Get My 15% OFF'}
            </button>
          </form>

          <p className="text-xs text-gray-500 mt-4">
            No spam, unsubscribe anytime. By subscribing, you agree to our privacy policy.
          </p>
        </div>
      </div>
    </div>
  );
};

// Abandoned Cart Recovery Component
export const AbandonedCartReminder = ({ cartItems, onReturn }) => {
  const [showReminder, setShowReminder] = useState(false);

  useEffect(() => {
    if (cartItems.length > 0) {
      // Show reminder after 5 minutes of inactivity
      const timer = setTimeout(() => {
        setShowReminder(true);
      }, 5 * 60 * 1000);

      return () => clearTimeout(timer);
    }
  }, [cartItems]);

  const handleReturn = () => {
    setShowReminder(false);
    onReturn();
  };

  if (!showReminder) return null;

  const totalItems = cartItems.reduce((sum, item) => sum + item.quantity, 0);
  const totalValue = cartItems.reduce((sum, item) => sum + (item.price * item.quantity), 0);

  return (
    <div className="fixed bottom-4 right-4 bg-white border border-gray-200 rounded-lg shadow-lg p-4 max-w-sm z-50">
      <button 
        onClick={() => setShowReminder(false)}
        className="absolute top-2 right-2 p-1 hover:bg-gray-100 rounded-full"
      >
        <X className="w-4 h-4 text-gray-500" />
      </button>
      
      <div className="pr-6">
        <h3 className="font-semibold text-gray-800 mb-2">
          Don't forget your items!
        </h3>
        <p className="text-sm text-gray-600 mb-3">
          You have {totalItems} items worth Rs. {totalValue.toLocaleString()} waiting in your cart
        </p>
        
        <div className="flex gap-2">
          <button
            onClick={handleReturn}
            className="flex-1 bg-accent-500 text-white py-2 px-4 rounded-lg hover:bg-accent-600 transition-colors text-sm font-medium"
          >
            Complete Purchase
          </button>
          <button
            onClick={() => setShowReminder(false)}
            className="px-4 py-2 text-gray-500 hover:text-gray-700 text-sm"
          >
            Later
          </button>
        </div>
      </div>
    </div>
  );
};

// Social Proof Component
export const SocialProof = ({ product, className = "" }) => {
  const [testimonials] = useState([
    {
      name: "Sarah Ahmed",
      rating: 5,
      comment: "Amazing quality! The fabric is so soft and the fit is perfect.",
      verified: true,
      image: "/api/placeholder/40/40"
    },
    {
      name: "Zara Khan",
      rating: 5,
      comment: "Loved the design and the delivery was super fast!",
      verified: true,
      image: "/api/placeholder/40/40"
    },
    {
      name: "Ayesha Malik",
      rating: 4,
      comment: "Great product, exactly as shown in pictures.",
      verified: true,
      image: "/api/placeholder/40/40"
    }
  ]);

  return (
    <div className={`bg-white rounded-lg shadow-md p-6 ${className}`}>
      <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center gap-2">
        <Star className="w-5 h-5 text-yellow-500" />
        What Our Customers Say
      </h3>
      
      <div className="space-y-4">
        {testimonials.map((testimonial, index) => (
          <div key={index} className="border-b border-gray-100 pb-4 last:border-b-0">
            <div className="flex items-center gap-3 mb-2">
              <img
                src={testimonial.image}
                alt={testimonial.name}
                className="w-10 h-10 rounded-full"
              />
              <div className="flex-1">
                <div className="flex items-center gap-2">
                  <span className="font-medium text-gray-800">{testimonial.name}</span>
                  {testimonial.verified && (
                    <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full">
                      Verified
                    </span>
                  )}
                </div>
                <div className="flex items-center gap-1">
                  {[...Array(5)].map((_, i) => (
                    <Star
                      key={i}
                      className={`w-4 h-4 ${
                        i < testimonial.rating ? 'text-yellow-500 fill-current' : 'text-gray-300'
                      }`}
                    />
                  ))}
                </div>
              </div>
            </div>
            <p className="text-sm text-gray-600 italic">"{testimonial.comment}"</p>
          </div>
        ))}
      </div>
      
      <div className="mt-4 p-3 bg-blue-50 rounded-lg">
        <p className="text-sm text-blue-800">
          <span className="font-semibold">4.8/5 stars</span> from 1,200+ verified customers
        </p>
      </div>
    </div>
  );
};

// Discount Offer Banner Component
export const DiscountOfferBanner = ({ offer, onClose, className = "" }) => {
  if (!offer) return null;

  return (
    <div className={`bg-gradient-to-r from-red-500 to-red-600 text-white p-4 relative ${className}`}>
      <button 
        onClick={onClose}
        className="absolute top-2 right-2 p-1 hover:bg-white hover:bg-opacity-20 rounded-full transition-colors"
      >
        <X className="w-4 h-4" />
      </button>
      
      <div className="text-center">
        <div className="flex items-center justify-center gap-2 mb-2">
          <Zap className="w-5 h-5" />
          <span className="font-bold text-lg">{offer.title}</span>
        </div>
        <p className="text-sm opacity-90">{offer.description}</p>
        {offer.code && (
          <div className="mt-2">
            <span className="bg-white text-red-600 px-3 py-1 rounded-full text-sm font-bold">
              Code: {offer.code}
            </span>
          </div>
        )}
      </div>
    </div>
  );
};

// Progress Bar for Free Shipping
export const FreeShippingProgress = ({ currentTotal, freeShippingThreshold = 2000 }) => {
  const progress = Math.min((currentTotal / freeShippingThreshold) * 100, 100);
  const remaining = Math.max(freeShippingThreshold - currentTotal, 0);

  if (currentTotal >= freeShippingThreshold) {
    return (
      <div className="bg-green-50 border border-green-200 rounded-lg p-3 flex items-center gap-2">
        <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
          <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
          </svg>
        </div>
        <span className="text-sm text-green-700 font-medium">
          You've qualified for FREE shipping! 🎉
        </span>
      </div>
    );
  }

  return (
    <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
      <div className="flex items-center justify-between mb-2">
        <span className="text-sm text-blue-800">
          Add Rs. {remaining.toLocaleString()} more for FREE shipping
        </span>
        <span className="text-sm text-blue-600 font-medium">
          {Math.round(progress)}%
        </span>
      </div>
      <div className="w-full bg-blue-200 rounded-full h-2">
        <div 
          className="bg-blue-500 h-2 rounded-full transition-all duration-300"
          style={{ width: `${progress}%` }}
        />
      </div>
    </div>
  );
};

export default UrgencyIndicators;
