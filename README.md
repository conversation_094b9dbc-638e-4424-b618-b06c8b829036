# 🛍️ ShopHub - Girls' Fashion E-commerce Frontend

<div align="center">
  <img src="public/shop.png" alt="ShopHub Logo" width="100" height="100">
  
  **A modern React + Vite e-commerce frontend for girls' fashion**
  
  Inspired by Limelight.pk with beautiful pink/purple aesthetics
  
  [![React](https://img.shields.io/badge/React-18.0-blue.svg)](https://reactjs.org/)
  [![Vite](https://img.shields.io/badge/Vite-Latest-green.svg)](https://vitejs.dev/)
  [![Tailwind CSS](https://img.shields.io/badge/Tailwind-CSS-blue.svg)](https://tailwindcss.com/)
  [![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)
</div>

## ✨ Features

### 🛒 E-commerce Functionality
- **Product Catalog**: Browse extensive collection of fashion items
- **Smart Search**: Advanced search with filters and suggestions
- **Shopping Cart**: Add/remove items with real-time updates
- **Wishlist**: Save favorite items for later
- **Quick View**: Preview products without leaving the page

### 🎨 User Experience
- **Responsive Design**: Optimized for mobile, tablet, and desktop
- **Beautiful Animations**: Smooth transitions and micro-interactions
- **Category Filtering**: Easy navigation through product categories
- **Product Zoom**: High-quality image viewing
- **Size Guide**: Detailed sizing information

### 🇵🇰 Pakistani Market Focus
- **Currency**: Pakistani Rupees (PKR) formatting
- **Local Payment**: EasyPaisa, JazzCash integration ready
- **Free Shipping**: On orders over Rs. 3,000
- **Eastern & Western**: Diverse fashion categories

## 🚀 Tech Stack

- **Frontend**: React 18 with Hooks
- **Build Tool**: Vite (Lightning fast)
- **Styling**: Tailwind CSS with custom configurations
- **Routing**: React Router DOM
- **Icons**: Heroicons
- **Fonts**: Poppins (Google Fonts)
- **Animation**: Custom CSS animations
- **State Management**: React Context API

## 📱 Fashion Categories

### 👗 Eastern Wear
- Kurtis & Kurtas
- Suits & Salwar Kameez
- Sarees & Lehengas
- Dupattas & Shawls

### 👕 Western Wear
- Tops & Blouses
- Dresses & Gowns
- Jeans & Trousers
- Skirts & Shorts

### 💍 Accessories
- Handbags & Purses
- Jewelry & Watches
- Shoes & Sandals
- Scarves & Hijabs

### 🆕 Special Collections
- New Arrivals
- Sale Items
- Trending Now
- Staff Picks

## 🏁 Quick Start

### Prerequisites
- Node.js (v16 or higher)
- npm or yarn

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/Qaswar-01/ShopHub-Girls-Fashion-E-commerce-Frontend---React-Vite-.git
   cd ShopHub-Girls-Fashion-E-commerce-Frontend---React-Vite-
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Start development server**
   ```bash
   npm run dev
   ```

4. **Open in browser**
   ```
   http://localhost:5173
   ```

### Build for Production
```bash
npm run build
npm run preview
```

## 📁 Project Structure

```
src/
├── components/           # Reusable UI components
│   ├── Navbar.jsx       # Navigation with categories
│   ├── ProductCard.jsx  # Product display card
│   ├── Banner.jsx       # Hero banner
│   ├── Footer.jsx       # Site footer
│   ├── ProductQuickView.jsx
│   ├── EnhancedCartDrawer.jsx
│   ├── AdvancedSearchBar.jsx
│   └── ...
├── pages/               # Route pages
│   ├── Home.jsx         # Homepage
│   ├── ProductDetails.jsx
│   ├── Category.jsx     # Category listings
│   ├── Cart.jsx         # Shopping cart
│   ├── SearchResults.jsx
│   └── Wishlist.jsx
├── context/             # React Context
│   ├── CartContext.jsx  # Cart state management
│   ├── SearchContext.jsx
│   └── WishlistContext.jsx
├── data/                # Static data
│   ├── products.js      # Product catalog
│   └── categories.js    # Category definitions
├── services/            # API services
│   └── api.js          # API configurations
├── App.jsx             # Main app component
└── main.jsx           # Entry point
```

## 🎨 Design System

### Color Palette
- **Primary**: Pink gradients (#FF69B4, #FF1493)
- **Secondary**: Purple accents (#8B5CF6, #A855F7)
- **Neutral**: Grays for text and backgrounds
- **Success**: Green for positive actions
- **Warning**: Orange for alerts

### Typography
- **Primary Font**: Poppins (Google Fonts)
- **Weights**: 300, 400, 500, 600, 700
- **Responsive**: Scales with screen size

### Components
- **Cards**: Rounded corners with subtle shadows
- **Buttons**: Gradient backgrounds with hover effects
- **Forms**: Clean inputs with validation states
- **Navigation**: Sticky header with smooth scrolling

## 🔧 Development Scripts

```bash
# Development
npm run dev          # Start dev server
npm run build        # Build for production
npm run preview      # Preview production build

# Code Quality
npm run lint         # Run ESLint
npm run lint:fix     # Fix linting issues
```

## 📱 Responsive Breakpoints

- **Mobile**: 320px - 768px
- **Tablet**: 768px - 1024px
- **Desktop**: 1024px+

## 🚀 Performance Features

- **Code Splitting**: Lazy loading for better performance
- **Image Optimization**: Lazy loading and WebP support
- **Bundle Optimization**: Tree shaking and minification
- **Caching**: Browser and CDN caching strategies

## 🛡️ Security & Best Practices

- **Input Validation**: Form validation and sanitization
- **Secure Routing**: Protected routes and authentication ready
- **HTTPS Ready**: SSL/TLS configuration
- **Environment Variables**: Secure configuration management

## 📊 Analytics & SEO

- **SEO Optimized**: Meta tags and structured data
- **Analytics Ready**: Google Analytics integration
- **Social Media**: Open Graph and Twitter cards
- **Performance**: Lighthouse optimization

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **Inspiration**: Limelight.pk for design aesthetics
- **Framework**: React team for the amazing framework
- **Build Tool**: Vite for lightning-fast development
- **Styling**: Tailwind CSS for utility-first approach

## 📞 Contact

- **GitHub**: [@Qaswar-01](https://github.com/Qaswar-01)
- **Repository**: [ShopHub Fashion E-commerce](https://github.com/Qaswar-01/ShopHub-Girls-Fashion-E-commerce-Frontend---React-Vite-)

---

<div align="center">
  <p><strong>Made with ❤️ for fashion lovers</strong></p>
  <p>© 2025 ShopHub - Girls' Fashion E-commerce Frontend</p>
</div>