import React, { useState, useEffect, useRef } from 'react';
import { Search, X, Clock, TrendingUp, Filter } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { useSearch } from '../context/SearchContext';
import products from '../data/products';

const AdvancedSearchBar = ({ onSearchSubmit, className = "" }) => {
  const [query, setQuery] = useState('');
  const [suggestions, setSuggestions] = useState([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [searchHistory, setSearchHistory] = useState([]);
  const [popularSearches] = useState([
    'kurtis', 'dresses', 'tops', 'suits', 'eastern wear', 'western wear', 'accessories', 'sale'
  ]);
  const searchRef = useRef(null);
  const navigate = useNavigate();
  const { addToSearchHistory } = useSearch();

  useEffect(() => {
    const history = JSON.parse(localStorage.getItem('searchHistory') || '[]');
    setSearchHistory(history.slice(0, 5));
  }, []);

  useEffect(() => {
    if (query.length > 0) {
      const filteredProducts = products.filter(product =>
        product.name.toLowerCase().includes(query.toLowerCase()) ||
        product.category.toLowerCase().includes(query.toLowerCase()) ||
        product.subcategory?.toLowerCase().includes(query.toLowerCase())
      ).slice(0, 6);

      const categoryMatches = [...new Set(products
        .filter(product => 
          product.category.toLowerCase().includes(query.toLowerCase()) ||
          product.subcategory?.toLowerCase().includes(query.toLowerCase())
        )
        .map(product => product.category)
      )].slice(0, 3);

      setSuggestions({
        products: filteredProducts,
        categories: categoryMatches
      });
      setShowSuggestions(true);
    } else {
      setSuggestions({ products: [], categories: [] });
      setShowSuggestions(false);
    }
  }, [query]);

  const handleSubmit = (e) => {
    e.preventDefault();
    if (query.trim()) {
      handleSearch(query.trim());
    }
  };

  const handleSearch = (searchQuery) => {
    addToSearchHistory(searchQuery);
    const updatedHistory = [searchQuery, ...searchHistory.filter(h => h !== searchQuery)].slice(0, 5);
    setSearchHistory(updatedHistory);
    localStorage.setItem('searchHistory', JSON.stringify(updatedHistory));
    
    onSearchSubmit ? onSearchSubmit(searchQuery) : navigate(`/search?q=${encodeURIComponent(searchQuery)}`);
    setQuery('');
    setShowSuggestions(false);
  };

  const handleProductClick = (productId) => {
    navigate(`/product/${productId}`);
    setQuery('');
    setShowSuggestions(false);
  };

  const handleCategoryClick = (category) => {
    navigate(`/category/${category.toLowerCase().replace(/\s+/g, '-')}`);
    setQuery('');
    setShowSuggestions(false);
  };

  const clearHistory = () => {
    setSearchHistory([]);
    localStorage.removeItem('searchHistory');
  };

  return (
    <div className={`relative ${className}`} ref={searchRef}>
      <form onSubmit={handleSubmit} className="relative">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-secondary-400" />
          <input
            type="text"
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            onFocus={() => setShowSuggestions(true)}
            placeholder="Search for products, categories..."
            className="w-full pl-10 pr-4 py-2 border border-primary-200 rounded-full focus:outline-none focus:ring-2 focus:ring-accent-400 focus:border-transparent bg-white/80 backdrop-blur-sm transition-all duration-200"
          />
          {query && (
            <button
              type="button"
              onClick={() => {
                setQuery('');
                setShowSuggestions(false);
              }}
              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-secondary-400 hover:text-secondary-600"
            >
              <X className="w-4 h-4" />
            </button>
          )}
        </div>
      </form>

      {/* Search Suggestions Dropdown */}
      {showSuggestions && (
        <div className="absolute top-full left-0 right-0 mt-1 bg-white border border-primary-200 rounded-xl shadow-lg z-50 max-h-96 overflow-y-auto">
          {query.length === 0 && (
            <div className="p-4">
              {/* Search History */}
              {searchHistory.length > 0 && (
                <div className="mb-4">
                  <div className="flex items-center justify-between mb-2">
                    <h3 className="text-sm font-semibold text-secondary-700 flex items-center gap-2">
                      <Clock className="w-4 h-4" />
                      Recent Searches
                    </h3>
                    <button
                      onClick={clearHistory}
                      className="text-xs text-secondary-500 hover:text-secondary-700"
                    >
                      Clear
                    </button>
                  </div>
                  <div className="space-y-1">
                    {searchHistory.map((item, index) => (
                      <button
                        key={index}
                        onClick={() => handleSearch(item)}
                        className="w-full text-left px-3 py-2 text-sm text-secondary-600 hover:bg-primary-50 rounded-lg transition-colors"
                      >
                        {item}
                      </button>
                    ))}
                  </div>
                </div>
              )}

              {/* Popular Searches */}
              <div>
                <h3 className="text-sm font-semibold text-secondary-700 mb-2 flex items-center gap-2">
                  <TrendingUp className="w-4 h-4" />
                  Popular Searches
                </h3>
                <div className="flex flex-wrap gap-2">
                  {popularSearches.map((item, index) => (
                    <button
                      key={index}
                      onClick={() => handleSearch(item)}
                      className="px-3 py-1 text-xs bg-primary-100 text-secondary-600 rounded-full hover:bg-primary-200 transition-colors"
                    >
                      {item}
                    </button>
                  ))}
                </div>
              </div>
            </div>
          )}

          {query.length > 0 && (
            <div className="p-4">
              {/* Categories */}
              {suggestions.categories.length > 0 && (
                <div className="mb-4">
                  <h3 className="text-sm font-semibold text-secondary-700 mb-2">Categories</h3>
                  <div className="space-y-1">
                    {suggestions.categories.map((category, index) => (
                      <button
                        key={index}
                        onClick={() => handleCategoryClick(category)}
                        className="w-full text-left px-3 py-2 text-sm text-secondary-600 hover:bg-primary-50 rounded-lg transition-colors flex items-center gap-2"
                      >
                        <Filter className="w-4 h-4" />
                        {category}
                      </button>
                    ))}
                  </div>
                </div>
              )}

              {/* Products */}
              {suggestions.products.length > 0 && (
                <div>
                  <h3 className="text-sm font-semibold text-secondary-700 mb-2">Products</h3>
                  <div className="space-y-2">
                    {suggestions.products.map((product) => (
                      <button
                        key={product.id}
                        onClick={() => handleProductClick(product.id)}
                        className="w-full text-left p-3 hover:bg-primary-50 rounded-lg transition-colors flex items-center gap-3"
                      >
                        <img
                          src={product.image}
                          alt={product.name}
                          className="w-10 h-10 object-cover rounded-lg"
                        />
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-medium text-secondary-800 truncate">
                            {product.name}
                          </p>
                          <p className="text-xs text-secondary-500 truncate">
                            {product.category} • Rs. {product.price.toLocaleString()}
                          </p>
                        </div>
                      </button>
                    ))}
                  </div>
                </div>
              )}

              {/* No Results */}
              {suggestions.products.length === 0 && suggestions.categories.length === 0 && (
                <div className="text-center py-4">
                  <p className="text-sm text-secondary-500">No results found for "{query}"</p>
                  <button
                    onClick={() => handleSearch(query)}
                    className="mt-2 text-sm text-accent-600 hover:text-accent-700"
                  >
                    Search anyway
                  </button>
                </div>
              )}
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default AdvancedSearchBar;
