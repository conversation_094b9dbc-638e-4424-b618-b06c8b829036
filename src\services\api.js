// API Configuration and Services
const API_BASE_URL = process.env.REACT_APP_API_URL || 'https://api.shophub.com';

// HTTP Client with error handling
class ApiClient {
  constructor(baseURL = API_BASE_URL) {
    this.baseURL = baseURL;
    this.headers = {
      'Content-Type': 'application/json',
    };
  }

  async request(endpoint, options = {}) {
    const url = `${this.baseURL}${endpoint}`;
    const config = {
      headers: this.headers,
      ...options,
    };

    try {
      const response = await fetch(url, config);
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error('API request failed:', error);
      throw error;
    }
  }

  // GET request
  async get(endpoint, params = {}) {
    const query = new URLSearchParams(params).toString();
    const url = query ? `${endpoint}?${query}` : endpoint;
    
    return this.request(url, {
      method: 'GET',
    });
  }

  // POST request
  async post(endpoint, data = {}) {
    return this.request(endpoint, {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  // PUT request
  async put(endpoint, data = {}) {
    return this.request(endpoint, {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  }

  // DELETE request
  async delete(endpoint) {
    return this.request(endpoint, {
      method: 'DELETE',
    });
  }
}

// Create API client instance
const apiClient = new ApiClient();

// Product API Services
export const productApi = {
  // Get all products with filters
  getProducts: async (filters = {}) => {
    try {
      return await apiClient.get('/products', filters);
    } catch (error) {
      // Fallback to local data if API fails
      const products = await import('../data/products');
      return { data: products.products || [], total: products.products?.length || 0 };
    }
  },

  // Get single product
  getProduct: async (id) => {
    try {
      return await apiClient.get(`/products/${id}`);
    } catch (error) {
      // Fallback to local data
      const products = await import('../data/products');
      const product = products.products?.find(p => p.id == id);
      return { data: product };
    }
  },

  // Search products
  searchProducts: async (query, filters = {}) => {
    try {
      return await apiClient.get('/products/search', { q: query, ...filters });
    } catch (error) {
      // Fallback to local search
      const products = await import('../data/products');
      const filtered = products.products?.filter(product => 
        product.name.toLowerCase().includes(query.toLowerCase()) ||
        product.category.toLowerCase().includes(query.toLowerCase()) ||
        product.description.toLowerCase().includes(query.toLowerCase())
      ) || [];
      return { data: filtered, total: filtered.length };
    }
  },

  // Get product recommendations
  getRecommendations: async (productId, type = 'similar') => {
    try {
      return await apiClient.get(`/products/${productId}/recommendations`, { type });
    } catch (error) {
      // Fallback logic for recommendations
      const products = await import('../data/products');
      const currentProduct = products.products?.find(p => p.id == productId);
      const similar = products.products?.filter(p => 
        p.id !== productId && p.category === currentProduct?.category
      ).slice(0, 4) || [];
      return { data: similar };
    }
  },

  // Get trending products
  getTrending: async (limit = 10) => {
    try {
      return await apiClient.get('/products/trending', { limit });
    } catch (error) {
      // Fallback to local data
      const products = await import('../data/products');
      const trending = products.products?.filter(p => p.isNew || p.rating > 4.5).slice(0, limit) || [];
      return { data: trending };
    }
  },
};

// Order API Services
export const orderApi = {
  // Create new order
  createOrder: async (orderData) => {
    try {
      return await apiClient.post('/orders', orderData);
    } catch (error) {
      // Simulate order creation for demo
      return {
        data: {
          id: 'ORDER-' + Math.random().toString(36).substr(2, 9),
          status: 'confirmed',
          ...orderData,
          createdAt: new Date().toISOString()
        }
      };
    }
  },

  // Get order by ID
  getOrder: async (orderId) => {
    try {
      return await apiClient.get(`/orders/${orderId}`);
    } catch (error) {
      // Fallback demo order
      return {
        data: {
          id: orderId,
          status: 'processing',
          items: [],
          total: 0,
          createdAt: new Date().toISOString()
        }
      };
    }
  },

  // Get order history
  getOrderHistory: async (userId) => {
    try {
      return await apiClient.get(`/users/${userId}/orders`);
    } catch (error) {
      return { data: [] };
    }
  },

  // Track order
  trackOrder: async (orderId) => {
    try {
      return await apiClient.get(`/orders/${orderId}/tracking`);
    } catch (error) {
      // Demo tracking data
      return {
        data: {
          status: 'shipped',
          trackingNumber: 'TRK' + Math.random().toString(36).substr(2, 9),
          estimatedDelivery: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toISOString()
        }
      };
    }
  },
};

// User API Services
export const userApi = {
  // User registration
  register: async (userData) => {
    try {
      return await apiClient.post('/auth/register', userData);
    } catch (error) {
      // Demo registration
      return {
        data: {
          user: { id: 'USER-' + Math.random().toString(36).substr(2, 9), ...userData },
          token: 'demo-token-' + Math.random().toString(36).substr(2, 9)
        }
      };
    }
  },

  // User login
  login: async (credentials) => {
    try {
      return await apiClient.post('/auth/login', credentials);
    } catch (error) {
      // Demo login
      return {
        data: {
          user: { id: 'USER-123', email: credentials.email, name: 'Demo User' },
          token: 'demo-token-' + Math.random().toString(36).substr(2, 9)
        }
      };
    }
  },

  // Get user profile
  getProfile: async (userId) => {
    try {
      return await apiClient.get(`/users/${userId}`);
    } catch (error) {
      return {
        data: {
          id: userId,
          name: 'Demo User',
          email: '<EMAIL>',
          preferences: {}
        }
      };
    }
  },

  // Update user profile
  updateProfile: async (userId, profileData) => {
    try {
      return await apiClient.put(`/users/${userId}`, profileData);
    } catch (error) {
      return { data: { id: userId, ...profileData } };
    }
  },
};

// Newsletter API Services
export const newsletterApi = {
  // Subscribe to newsletter
  subscribe: async (email) => {
    try {
      return await apiClient.post('/newsletter/subscribe', { email });
    } catch (error) {
      // Demo subscription
      return { data: { success: true, message: 'Subscribed successfully!' } };
    }
  },

  // Unsubscribe from newsletter
  unsubscribe: async (email) => {
    try {
      return await apiClient.post('/newsletter/unsubscribe', { email });
    } catch (error) {
      return { data: { success: true, message: 'Unsubscribed successfully!' } };
    }
  },
};

// Inventory API Services
export const inventoryApi = {
  // Check stock availability
  checkStock: async (productId, size, color) => {
    try {
      return await apiClient.get(`/inventory/${productId}`, { size, color });
    } catch (error) {
      // Demo stock check
      return {
        data: {
          available: Math.floor(Math.random() * 10) + 1,
          inStock: true
        }
      };
    }
  },

  // Reserve stock
  reserveStock: async (items) => {
    try {
      return await apiClient.post('/inventory/reserve', { items });
    } catch (error) {
      return { data: { success: true, reservationId: 'RES-' + Math.random().toString(36).substr(2, 9) } };
    }
  },
};

// Analytics API Services
export const analyticsApi = {
  // Track event
  trackEvent: async (eventName, eventData) => {
    try {
      return await apiClient.post('/analytics/events', { eventName, eventData, timestamp: new Date().toISOString() });
    } catch (error) {
      console.log('Analytics tracking failed:', error);
      return { data: { success: true } };
    }
  },

  // Track page view
  trackPageView: async (page, userId = null) => {
    try {
      return await apiClient.post('/analytics/pageviews', { page, userId, timestamp: new Date().toISOString() });
    } catch (error) {
      console.log('Page view tracking failed:', error);
      return { data: { success: true } };
    }
  },

  // Get analytics data
  getAnalytics: async (dateRange = '7d') => {
    try {
      return await apiClient.get('/analytics', { dateRange });
    } catch (error) {
      return { data: { views: 0, conversions: 0, revenue: 0 } };
    }
  },
};

// Review API Services
export const reviewApi = {
  // Get product reviews
  getProductReviews: async (productId, page = 1, limit = 10) => {
    try {
      return await apiClient.get(`/products/${productId}/reviews`, { page, limit });
    } catch (error) {
      // Demo reviews
      return {
        data: [
          {
            id: 1,
            user: 'Sarah A.',
            rating: 5,
            comment: 'Amazing quality! Highly recommend.',
            date: '2024-01-15',
            verified: true
          },
          {
            id: 2,
            user: 'Zara K.',
            rating: 4,
            comment: 'Great product, fast delivery!',
            date: '2024-01-10',
            verified: true
          }
        ],
        total: 2,
        average: 4.5
      };
    }
  },

  // Submit product review
  submitReview: async (productId, reviewData) => {
    try {
      return await apiClient.post(`/products/${productId}/reviews`, reviewData);
    } catch (error) {
      return {
        data: {
          id: Math.random().toString(36).substr(2, 9),
          ...reviewData,
          date: new Date().toISOString()
        }
      };
    }
  },
};

// Cache mechanism for API responses
class ApiCache {
  constructor() {
    this.cache = new Map();
    this.ttl = 5 * 60 * 1000; // 5 minutes default TTL
  }

  set(key, data, ttl = this.ttl) {
    this.cache.set(key, {
      data,
      expiry: Date.now() + ttl
    });
  }

  get(key) {
    const cached = this.cache.get(key);
    if (cached && cached.expiry > Date.now()) {
      return cached.data;
    }
    this.cache.delete(key);
    return null;
  }

  clear() {
    this.cache.clear();
  }
}

export const apiCache = new ApiCache();

// Error handling utility
export const handleApiError = (error, fallbackData = null) => {
  console.error('API Error:', error);
  
  // Log error to analytics
  analyticsApi.trackEvent('api_error', {
    error: error.message,
    timestamp: new Date().toISOString()
  });

  // Return fallback data or throw error
  if (fallbackData !== null) {
    return fallbackData;
  }
  
  throw error;
};

export default apiClient;
