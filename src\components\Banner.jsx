import React from 'react';
import { Link } from 'react-router-dom';
import { Truck, RotateCcw, Clock, Check, Package, Shield, Shirt, Gem, Sparkles } from 'lucide-react';
import { FadeIn, StaggerContainer, ScaleIn, FloatingElement } from './AnimationSystem';

const Banner = () => {
  return (
    <div className="relative">
      {/* Main Hero Banner */}
      <div className="relative bg-gradient-to-r from-primary-50 via-primary-100 to-primary-200 text-secondary-700 overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 bg-gradient-to-br from-primary-100/30 via-primary-200/20 to-primary-300/30"></div>
        
        {/* Decorative Elements */}
        <div className="absolute top-0 right-0 w-64 h-64 bg-gradient-to-br from-primary-200/30 to-accent-200/30 rounded-full -mr-32 -mt-32"></div>
        <div className="absolute bottom-0 left-0 w-48 h-48 bg-gradient-to-tr from-accent-100/40 to-primary-100/40 rounded-full -ml-24 -mb-24"></div>
        
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-col lg:flex-row items-center justify-between py-20">
            {/* Left Content */}
            <div className="lg:w-1/2 text-center lg:text-left mb-12 lg:mb-0 z-10">
              <FadeIn delay={100}>
                <div className="mb-6">
                  <span className="inline-block bg-gradient-to-r from-accent-400 to-accent-500 text-white px-4 py-2 rounded-full text-sm font-bold uppercase tracking-wide shadow-lg animate-pulse-glow">
                    Summer Collection 2024
                  </span>
                </div>
              </FadeIn>
              
              <FadeIn delay={200}>
                <h1 className="text-5xl md:text-7xl font-bold mb-6 leading-tight text-secondary-800">
                  Latest Fashion
                  <br />
                  <span className="text-accent-500 relative">
                    Trends
                    <div className="absolute -bottom-2 left-0 w-full h-3 bg-gradient-to-r from-accent-300 to-accent-400 opacity-50 rounded"></div>
                  </span>
                </h1>
              </FadeIn>
              
              <FadeIn delay={300}>
                <p className="text-xl md:text-2xl mb-4 text-secondary-600 font-light">
                  Discover the hottest styles in Eastern & Western wear
                </p>
              </FadeIn>
              
              <FadeIn delay={400}>
                <p className="text-lg mb-8 text-secondary-500 max-w-md">
                  From elegant kurtis to trendy dresses - find your perfect look with our exclusive collection crafted for the modern woman.
                </p>
              </FadeIn>
              
              <FadeIn delay={500}>
                <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
                  <Link 
                    to="/category/eastern"
                    className="bg-gradient-to-r from-accent-400 to-accent-500 text-white px-8 py-4 rounded-full font-bold hover:from-accent-500 hover:to-accent-600 transition-spring duration-300 transform hover:scale-105 shadow-lg text-center"
                  >
                    Shop Eastern Wear
                  </Link>
                  <Link 
                    to="/category/western"
                    className="border-2 border-accent-400 text-accent-500 px-8 py-4 rounded-full font-bold hover:bg-accent-400 hover:text-white transition-spring duration-300 shadow-lg text-center"
                  >
                    Shop Western Wear
                  </Link>
                </div>
              </FadeIn>
              
              <StaggerContainer staggerDelay={100} className="mt-8">
                <div className="flex items-center justify-center lg:justify-start space-x-6 text-secondary-500">
                  <div className="flex items-center">
                    <Truck className="w-5 h-5 mr-2 text-accent-500" />
                    <span className="text-sm">Free Shipping</span>
                  </div>
                  <div className="flex items-center">
                    <RotateCcw className="w-5 h-5 mr-2 text-accent-500" />
                    <span className="text-sm">Easy Returns</span>
                  </div>
                  <div className="flex items-center">
                    <Clock className="w-5 h-5 mr-2 text-accent-500" />
                    <span className="text-sm">Fast Delivery</span>
                  </div>
                </div>
              </StaggerContainer>
            </div>

            {/* Right Image */}
            <div className="lg:w-1/2 relative z-10">
              <FadeIn delay={600} direction="right">
                <div className="relative">
                  <div className="absolute inset-0 bg-gradient-to-r from-accent-200 to-primary-200 rounded-2xl transform rotate-3 scale-105 opacity-40"></div>
                  <img
                    src="https://hurf.pk/wp-content/uploads/2022/09/PSX_20220703_150702-scaled.jpg"
                    alt="Fashion Model"
                    className="relative w-full max-w-md mx-auto rounded-2xl shadow-2xl transition-transform duration-700 hover:scale-105"
                    onError={(e) => {
                      e.target.src = "https://picsum.photos/500/600?random=fashion";
                    }}
                  />
                  
                  {/* Floating Sale Badge */}
                  <FloatingElement className="absolute -top-6 -right-6">
                    <ScaleIn delay={800}>
                      <div className="bg-gradient-to-r from-accent-500 to-accent-600 text-white px-6 py-3 rounded-full font-bold shadow-lg transform rotate-12 animate-pulse-glow">
                        <span className="text-lg">50% OFF</span>
                      </div>
                    </ScaleIn>
                  </FloatingElement>
                  
                  {/* Floating New Badge */}
                  <FloatingElement className="absolute -bottom-6 -left-6">
                    <ScaleIn delay={900}>
                      <div className="bg-gradient-to-r from-secondary-600 to-secondary-700 text-white px-6 py-3 rounded-full font-bold shadow-lg transform -rotate-12 animate-float">
                        <span className="text-sm">New Collection</span>
                      </div>
                    </ScaleIn>
                  </FloatingElement>
                  
                  {/* Floating Price Tag */}
                  <FloatingElement className="absolute top-1/2 -left-8">
                    <ScaleIn delay={1000}>
                      <div className="bg-white/95 backdrop-blur-sm text-secondary-700 px-4 py-2 rounded-lg shadow-lg transform -rotate-6 border border-primary-200 hover:scale-110 transition-transform duration-300">
                        <span className="text-sm font-bold">Starting Rs. 4,999</span>
                      </div>
                    </ScaleIn>
                  </FloatingElement>
                </div>
              </FadeIn>
            </div>
          </div>
        </div>
      </div>

      {/* Seasonal Collection Banner */}
      <div className="relative bg-gradient-to-r from-secondary-50 via-secondary-100 to-secondary-50 text-secondary-700 border-t border-primary-100">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-col lg:flex-row items-center py-8 gap-8">
            <div className="flex-1 text-center lg:text-left">
              <h2 className="text-3xl lg:text-4xl font-bold mb-4 text-secondary-800">
                Monsoon Special
              </h2>
              <p className="text-lg lg:text-xl text-secondary-600 mb-6">
                Beat the heat with our breathable fabrics and vibrant colors
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
                <Link 
                  to="/category/sale"
                  className="bg-gradient-to-r from-primary-400 to-primary-500 text-white px-6 py-3 rounded-full font-semibold hover:from-primary-500 hover:to-primary-600 transition-all duration-200 text-center shadow-lg"
                >
                  Shop Now
                </Link>
                <a 
                  href="#category-highlights"
                  className="border-2 border-primary-400 text-primary-500 px-6 py-3 rounded-full font-semibold hover:bg-primary-400 hover:text-white transition-all duration-200 text-center"
                >
                  View Lookbook
                </a>
              </div>
            </div>
            <div className="flex-1 relative w-full">
              <img
                src="https://images.unsplash.com/photo-1551232864-3f0890e580d9?auto=format&fit=crop&w=500&q=80"
                alt="Monsoon Collection"
                className="w-full h-48 lg:h-64 object-cover object-center rounded-lg shadow-lg"
                onError={(e) => {
                  e.target.src = "https://picsum.photos/500/300?random=monsoon";
                }}
              />
              <div className="absolute top-4 right-4 bg-accent-500 text-white px-3 py-1 rounded-full text-sm font-bold">
                LIMITED TIME
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Features Section */}
      <div className="bg-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <StaggerContainer staggerDelay={200} className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {/* Free Shipping */}
            <div className="text-center group">
              <FloatingElement>
                <div className="bg-gradient-to-r from-primary-100 to-primary-200 rounded-full w-20 h-20 flex items-center justify-center mx-auto mb-4 group-hover:from-primary-200 group-hover:to-primary-300 transition-spring duration-300 hover:scale-110">
                  <Package className="w-10 h-10 text-primary-600" />
                </div>
              </FloatingElement>
              <h3 className="text-xl font-semibold text-secondary-700 mb-2">Free Shipping</h3>
              <p className="text-secondary-500">Complimentary shipping on orders over Rs. 3,000</p>
            </div>

            {/* Easy Returns */}
            <div className="text-center group">
              <FloatingElement>
                <div className="bg-gradient-to-r from-secondary-100 to-secondary-200 rounded-full w-20 h-20 flex items-center justify-center mx-auto mb-4 group-hover:from-secondary-200 group-hover:to-secondary-300 transition-spring duration-300 hover:scale-110">
                  <RotateCcw className="w-10 h-10 text-secondary-600" />
                </div>
              </FloatingElement>
              <h3 className="text-xl font-semibold text-secondary-700 mb-2">Easy Returns</h3>
              <p className="text-secondary-500">30-day hassle-free return policy with free exchanges</p>
            </div>

            {/* Quality Guarantee */}
            <div className="text-center group">
              <FloatingElement>
                <div className="bg-gradient-to-r from-accent-100 to-accent-200 rounded-full w-20 h-20 flex items-center justify-center mx-auto mb-4 group-hover:from-accent-200 group-hover:to-accent-300 transition-spring duration-300 hover:scale-110">
                  <Shield className="w-10 h-10 text-accent-600" />
                </div>
              </FloatingElement>
              <h3 className="text-xl font-semibold text-secondary-700 mb-2">Quality Guarantee</h3>
              <p className="text-secondary-500">Premium quality fabrics and expert craftsmanship</p>
            </div>
          </StaggerContainer>
        </div>
      </div>

      {/* Category Highlights */}
      <div id="category-highlights" className="bg-gray-50 py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-secondary-800 mb-4">
              Shop by Category
            </h2>
            <p className="text-xl text-secondary-600 max-w-2xl mx-auto">
              Explore our curated collections designed for every occasion and style preference
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {/* Eastern */}
            <Link to="/category/eastern" className="relative group cursor-pointer overflow-hidden rounded-2xl">
              <div className="bg-gradient-to-br from-primary-400 to-primary-600 p-8 text-white text-center transform group-hover:scale-105 transition-all duration-300 h-64 flex flex-col justify-center">
                <div className="absolute inset-0 bg-black bg-opacity-20 group-hover:bg-opacity-10 transition-all duration-300"></div>
                <div className="relative z-10">
                  <div className="mb-4">
                    <div className="w-16 h-16 bg-white bg-opacity-20 rounded-full flex items-center justify-center mx-auto">
                      <Shirt className="w-8 h-8" />
                    </div>
                  </div>
                  <h3 className="text-2xl font-bold mb-2">Eastern Wear</h3>
                  <p className="text-primary-100 mb-4">Kurtis • Suits • Sarees • Dupattas</p>
                  <span className="bg-white text-primary-600 px-6 py-2 rounded-full font-semibold hover:bg-primary-50 transition-colors duration-200 inline-block">
                    Explore Collection
                  </span>
                </div>
              </div>
            </Link>

            {/* Western */}
            <Link to="/category/western" className="relative group cursor-pointer overflow-hidden rounded-2xl">
              <div className="bg-gradient-to-br from-secondary-400 to-secondary-600 p-8 text-white text-center transform group-hover:scale-105 transition-all duration-300 h-64 flex flex-col justify-center">
                <div className="absolute inset-0 bg-black bg-opacity-20 group-hover:bg-opacity-10 transition-all duration-300"></div>
                <div className="relative z-10">
                  <div className="mb-4">
                    <div className="w-16 h-16 bg-white bg-opacity-20 rounded-full flex items-center justify-center mx-auto">
                      <Sparkles className="w-8 h-8" />
                    </div>
                  </div>
                  <h3 className="text-2xl font-bold mb-2">Western Wear</h3>
                  <p className="text-secondary-100 mb-4">Dresses • Tops • Jeans • Skirts</p>
                  <span className="bg-white text-secondary-600 px-6 py-2 rounded-full font-semibold hover:bg-secondary-50 transition-colors duration-200 inline-block">
                    Explore Collection
                  </span>
                </div>
              </div>
            </Link>

            {/* Accessories */}
            <Link to="/category/accessories" className="relative group cursor-pointer overflow-hidden rounded-2xl">
              <div className="bg-gradient-to-br from-accent-400 to-accent-600 p-8 text-white text-center transform group-hover:scale-105 transition-all duration-300 h-64 flex flex-col justify-center">
                <div className="absolute inset-0 bg-black bg-opacity-20 group-hover:bg-opacity-10 transition-all duration-300"></div>
                <div className="relative z-10">
                  <div className="mb-4">
                    <div className="w-16 h-16 bg-white bg-opacity-20 rounded-full flex items-center justify-center mx-auto">
                      <Gem className="w-8 h-8" />
                    </div>
                  </div>
                  <h3 className="text-2xl font-bold mb-2">Accessories</h3>
                  <p className="text-accent-100 mb-4">Bags • Jewelry • Shoes • Scarves</p>
                  <span className="bg-white text-accent-600 px-6 py-2 rounded-full font-semibold hover:bg-accent-50 transition-colors duration-200 inline-block">
                    Explore Collection
                  </span>
                </div>
              </div>
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Banner;
