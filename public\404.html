<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ShopHub - Girls' Fashion E-commerce</title>
    <script>
        // Single Page Apps for GitHub Pages
        // MIT License
        // https://github.com/rafgraph/spa-github-pages
        // This script checks to see if a redirect is present in the query string,
        // converts it back into the correct url and adds it to the
        // browser's history using window.history.replaceState(...)
        (function(l) {
            if (l.search[1] === '/' ) {
                var decoded = decodeURIComponent(l.search.slice(1));
                l.replace(l.protocol + '//' + l.hostname + (l.port ? ':' + l.port : '') + decoded);
            }
        }(window.location))
    </script>
</head>
<body>
    <div id="root"></div>
    <script>
        // Redirect to index.html for GitHub Pages
        window.location.replace('/ShopHub-Girls-Fashion-E-commerce-Frontend---React-Vite-/');
    </script>
</body>
</html>
