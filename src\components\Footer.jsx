import React from 'react';
import { Facebook, Instagram, Twitter, Youtube, Mail, Send } from 'lucide-react';

const Footer = () => {
  return (
    <footer className="bg-gradient-to-r from-secondary-800 via-secondary-700 to-secondary-800 text-white">
      {/* Main Footer */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Brand Section */}
          <div>
            <div className="flex items-center mb-4">
              <span className="text-2xl font-bold">
                <span className="text-primary-400">Shop</span>
                <span className="text-accent-400">Hub</span>
              </span>
            </div>
            <p className="text-secondary-300 mb-4">
              Your one-stop destination for the latest fashion trends in Eastern and Western wear.
            </p>
            <div className="flex space-x-4">
              {/* Social Media Icons */}
              <a href="#" className="text-secondary-400 hover:text-accent-400 transition-colors duration-200">
                <Facebook className="w-5 h-5" />
              </a>
              <a href="#" className="text-secondary-400 hover:text-accent-400 transition-colors duration-200">
                <Twitter className="w-5 h-5" />
              </a>
              <a href="#" className="text-secondary-400 hover:text-accent-400 transition-colors duration-200">
                <Instagram className="w-5 h-5" />
              </a>
              <a href="#" className="text-secondary-400 hover:text-accent-400 transition-colors duration-200">
                <Youtube className="w-5 h-5" />
              </a>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="text-lg font-semibold mb-4 text-white">Quick Links</h3>
            <ul className="space-y-2">
              <li><a href="/about" className="text-secondary-300 hover:text-accent-400 transition-colors duration-200">About Us</a></li>
              <li><a href="/contact" className="text-secondary-300 hover:text-accent-400 transition-colors duration-200">Contact</a></li>
              <li><a href="/size-guide" className="text-secondary-300 hover:text-accent-400 transition-colors duration-200">Size Guide</a></li>
              <li><a href="/faq" className="text-secondary-300 hover:text-accent-400 transition-colors duration-200">FAQ</a></li>
              <li><a href="/blog" className="text-secondary-300 hover:text-accent-400 transition-colors duration-200">Fashion Blog</a></li>
            </ul>
          </div>

          {/* Categories */}
          <div>
            <h3 className="text-lg font-semibold mb-4 text-white">Categories</h3>
            <ul className="space-y-2">
              <li><a href="/category/eastern" className="text-secondary-300 hover:text-accent-400 transition-colors duration-200">Eastern Wear</a></li>
              <li><a href="/category/western" className="text-secondary-300 hover:text-accent-400 transition-colors duration-200">Western Wear</a></li>
              <li><a href="/category/accessories" className="text-secondary-300 hover:text-accent-400 transition-colors duration-200">Accessories</a></li>
              <li><a href="/category/new-arrivals" className="text-secondary-300 hover:text-accent-400 transition-colors duration-200">New Arrivals</a></li>
              <li><a href="/category/sale" className="text-secondary-300 hover:text-accent-400 transition-colors duration-200">Sale</a></li>
            </ul>
          </div>

          {/* Customer Service */}
          <div>
            <h3 className="text-lg font-semibold mb-4 text-white">Customer Service</h3>
            <ul className="space-y-2">
              <li><a href="/shipping" className="text-secondary-300 hover:text-accent-400 transition-colors duration-200">Shipping Info</a></li>
              <li><a href="/returns" className="text-secondary-300 hover:text-accent-400 transition-colors duration-200">Returns & Exchanges</a></li>
              <li><a href="/privacy" className="text-secondary-300 hover:text-accent-400 transition-colors duration-200">Privacy Policy</a></li>
              <li><a href="/terms" className="text-secondary-300 hover:text-accent-400 transition-colors duration-200">Terms of Service</a></li>
              <li><a href="/track-order" className="text-secondary-300 hover:text-accent-400 transition-colors duration-200">Track Your Order</a></li>
            </ul>
          </div>
        </div>

        {/* Newsletter Signup */}
        <div className="border-t border-secondary-600 pt-8 mt-8">
          <div className="flex flex-col md:flex-row items-center justify-between">
            <div className="mb-4 md:mb-0">
              <h3 className="text-lg font-semibold mb-2 text-white">Stay Updated</h3>
              <p className="text-secondary-300">Subscribe to get special offers and latest fashion updates</p>
            </div>
            <div className="flex flex-col sm:flex-row gap-2 w-full md:w-auto">
              <div className="relative w-full md:w-64">
                <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-secondary-400 w-4 h-4" />
                <input
                  type="email"
                  placeholder="Enter your email"
                  className="pl-10 pr-4 py-2 bg-secondary-600/50 backdrop-blur-sm text-white placeholder-secondary-300 rounded-full focus:outline-none focus:ring-2 focus:ring-accent-400 w-full border border-secondary-500"
                />
              </div>
              <button className="bg-gradient-to-r from-accent-500 to-accent-600 text-white px-6 py-2 rounded-full hover:from-accent-600 hover:to-accent-700 transition-all duration-200 font-semibold shadow-lg flex items-center gap-2">
                <Send className="w-4 h-4" />
                Subscribe
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Bottom Bar */}
      <div className="border-t border-secondary-600 bg-secondary-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex flex-col md:flex-row items-center justify-between">
            <p className="text-secondary-300 text-sm">
              © 2024 ShopHub. All rights reserved.
            </p>
            <div className="flex items-center space-x-4 mt-2 md:mt-0">
              <span className="text-secondary-300 text-sm">We Accept:</span>
              <div className="flex space-x-2">
                <div className="bg-gradient-to-r from-white to-gray-50 rounded px-2 py-1 shadow-sm">
                  <span className="text-xs font-bold text-secondary-800">VISA</span>
                </div>
                <div className="bg-gradient-to-r from-white to-gray-50 rounded px-2 py-1 shadow-sm">
                  <span className="text-xs font-bold text-secondary-800">MC</span>
                </div>
                <div className="bg-gradient-to-r from-white to-gray-50 rounded px-2 py-1 shadow-sm">
                  <span className="text-xs font-bold text-secondary-800">EASYPAISA</span>
                </div>
                <div className="bg-gradient-to-r from-white to-gray-50 rounded px-2 py-1 shadow-sm">
                  <span className="text-xs font-bold text-secondary-800">JAZZCASH</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
