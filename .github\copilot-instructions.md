<!-- Use this file to provide workspace-specific custom instructions to <PERSON><PERSON><PERSON>. For more details, visit https://code.visualstudio.com/docs/copilot/copilot-customization#_use-a-githubcopilotinstructionsmd-file -->

# ShopHub - Girls' Fashion E-commerce Frontend

This is a React + Vite e-commerce frontend project targeting girls' fashion, inspired by Limelight.pk.

## Project Guidelines

- **Framework**: React 18 with Vite
- **Styling**: Tailwind CSS with custom pink/purple color scheme
- **Currency**: PKR (Pakistani Rupees)
- **Target Audience**: Girls' fashion (Eastern, Western, Accessories)
- **Design Style**: Modern, clean, inspired by Limelight.pk

## Component Structure

- Keep components modular and reusable
- Use modern React patterns (hooks, functional components)
- Apply consistent styling with Tailwind classes
- Ensure responsive design for mobile-first approach

## Naming Conventions

- Use PascalCase for component names
- Use camelCase for variables and functions
- Use kebab-case for CSS classes when needed
- Keep file names consistent with component names

## Fashion Categories

- Eastern (<PERSON><PERSON>, <PERSON>s, Sarees)
- Western (<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>)
- New Arrivals
- Sale Items
- Accessories (Bags, Jewelry, Shoes)

## Price Display

- Always show prices in PKR
- Use format: "PKR 2,999" or "Rs. 2,999"
- Show original price and discounted price for sale items
