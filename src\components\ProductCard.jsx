import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { Heart, Eye, ShoppingCart, ImageOff } from 'lucide-react';
import { useCart } from '../context/CartContext';
import { useWishlist } from '../context/WishlistContext';
import ProductQuickView from './ProductQuickView';
import { LazyImage } from './PerformanceOptimizations';
import { ScaleIn, FloatingElement } from './AnimationSystem';

const ProductCard = ({ product }) => {
  const [imageError, setImageError] = useState(false);
  const [isQuickViewOpen, setIsQuickViewOpen] = useState(false);
  const { addToCart } = useCart();
  const { toggleWishlist, isInWishlist } = useWishlist();
  
  const formatPrice = (price) => {
    return new Intl.NumberFormat('en-PK', {
      style: 'currency',
      currency: 'PKR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(price).replace('PKR', 'Rs.');
  };

  const handleImageError = () => {
    setImageError(true);
  };

  const handleAddToCart = (e) => {
    e.preventDefault();
    e.stopPropagation();
    addToCart(product, 1, product.sizes[0] || '', product.colors[0] || '');
  };

  const handleWishlistToggle = (e) => {
    e.preventDefault();
    e.stopPropagation();
    toggleWishlist(product);
  };

  const handleQuickView = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setIsQuickViewOpen(true);
  };

  return (
    <>
      <ScaleIn>
        <Link to={`/product/${product.id}`} className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-xl transition-shadow duration-300 group block h-full flex flex-col hover:scale-105 transition-transform">
        {/* Product Image */}
        <div className="relative overflow-hidden" style={{ height: '450px', width: '100%' }}>
          <LazyImage
            src={product.image}
            alt={product.name}
            className="w-full h-full object-cover object-top group-hover:scale-105 transition-transform duration-700"
            loadingClassName="bg-gradient-to-br from-secondary-100 to-secondary-200 animate-shimmer"
            errorClassName="bg-gradient-to-br from-secondary-100 to-secondary-200 flex items-center justify-center"
          />
          
          {/* Badges */}
          <div className="absolute top-2 left-2 flex flex-col gap-1">
            {product.isNew && (
              <FloatingElement>
                <span className="bg-gradient-to-r from-secondary-600 to-secondary-700 text-white text-xs font-bold px-2 py-1 rounded shadow-lg animate-bounce-in">
                  NEW
                </span>
              </FloatingElement>
            )}
            {product.isOnSale && (
              <FloatingElement>
                <span className="bg-gradient-to-r from-accent-500 to-accent-600 text-white text-xs font-bold px-2 py-1 rounded shadow-lg animate-pulse-glow">
                  SALE
                </span>
              </FloatingElement>
            )}
          </div>

          {/* Heart Icon */}
          <FloatingElement intensity="hover:-translate-y-1">
            <button 
              onClick={handleWishlistToggle}
              className={`absolute top-2 right-2 p-2 rounded-full shadow-lg transition-all duration-300 hover:scale-110 ${
                isInWishlist(product.id) 
                  ? 'bg-gradient-to-r from-accent-500 to-accent-600 text-white hover:from-accent-600 hover:to-accent-700' 
                  : 'bg-white/90 backdrop-blur-sm text-secondary-400 hover:bg-primary-50 hover:text-accent-500 border border-primary-100'
              }`}
              title={isInWishlist(product.id) ? 'Remove from Wishlist' : 'Add to Wishlist'}
            >
              <Heart className={`w-4 h-4 ${isInWishlist(product.id) ? 'fill-current' : ''}`} />
            </button>
          </FloatingElement>

          {/* Quick View Button */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
            <FloatingElement intensity="hover:-translate-y-1">
              <button 
                onClick={handleQuickView}
                className="bg-white/95 backdrop-blur-sm text-secondary-700 px-4 py-2 rounded-full hover:bg-white transition-all duration-200 shadow-lg font-semibold border border-primary-100 flex items-center gap-2 hover:scale-105"
              >
                <Eye className="w-4 h-4" />
                Quick View
              </button>
            </FloatingElement>
          </div>
      </div>

      {/* Product Details */}
      <div className="p-4 flex flex-col flex-grow bg-gradient-to-br from-white via-primary-50/30 to-primary-100/30">
        {/* Product Name */}
        <h3 className="text-lg font-semibold text-secondary-800 mb-2 hover:text-accent-500 transition-colors duration-200">
          {product.name}
        </h3>

        {/* Category */}
        <p className="text-sm text-secondary-500 mb-4 capitalize">{product.category}</p>

        {/* Price */}
        <div className="flex items-center justify-between mb-4 flex-grow">
          <div className="flex items-center space-x-2">
            <span className="text-lg font-bold text-secondary-800">
              {formatPrice(product.price)}
            </span>
            {product.isOnSale && (
              <span className="text-sm text-secondary-500 line-through">
                {formatPrice(product.originalPrice)}
              </span>
            )}
          </div>
          {product.isOnSale && (
            <span className="text-sm font-semibold text-accent-600 bg-accent-50 px-2 py-1 rounded-full">
              {Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100)}% OFF
            </span>
          )}
        </div>

        {/* Add to Cart Button */}
        <button 
          onClick={handleAddToCart}
          className="w-full bg-gradient-to-r from-accent-400 to-accent-500 text-white py-2 px-4 rounded-full hover:from-accent-500 hover:to-accent-600 transition-all duration-200 transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-accent-400 focus:ring-opacity-50 mt-auto font-semibold shadow-lg flex items-center justify-center gap-2"
        >
          <ShoppingCart className="w-4 h-4" />
          Add to Cart
        </button>
      </div>
    </Link>
    </ScaleIn>
    
    {/* Quick View Modal */}
    <ProductQuickView 
      product={product}
      isOpen={isQuickViewOpen}
      onClose={() => setIsQuickViewOpen(false)}
    />
  </>
  );
};

export default ProductCard;
