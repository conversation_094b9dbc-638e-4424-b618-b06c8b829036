import React, { useState, useEffect, useRef } from 'react';
import { ChevronUp } from 'lucide-react';

// Enhanced Animation Utilities
export const AnimationConfig = {
  // Timing functions
  easing: {
    easeInOut: 'cubic-bezier(0.4, 0, 0.2, 1)',
    easeOut: 'cubic-bezier(0, 0, 0.2, 1)',
    easeIn: 'cubic-bezier(0.4, 0, 1, 1)',
    bounce: 'cubic-bezier(0.68, -0.55, 0.265, 1.55)',
    spring: 'cubic-bezier(0.175, 0.885, 0.32, 1.275)',
  },
  
  // Duration presets
  duration: {
    fast: 150,
    normal: 300,
    slow: 500,
    slower: 750,
  },
  
  // Stagger delays for list animations
  stagger: {
    fast: 50,
    normal: 100,
    slow: 200,
  }
};

// Intersection Observer Hook for scroll animations
export const useIntersectionObserver = (options = {}) => {
  const [isIntersecting, setIsIntersecting] = useState(false);
  const [hasBeenVisible, setHasBeenVisible] = useState(false);
  const elementRef = useRef(null);

  useEffect(() => {
    const element = elementRef.current;
    if (!element) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        setIsIntersecting(entry.isIntersecting);
        if (entry.isIntersecting && !hasBeenVisible) {
          setHasBeenVisible(true);
        }
      },
      {
        threshold: 0.1,
        rootMargin: '50px',
        ...options,
      }
    );

    observer.observe(element);
    return () => observer.disconnect();
  }, [hasBeenVisible, options]);

  return { ref: elementRef, isIntersecting, hasBeenVisible };
};

// Fade In Animation Component
export const FadeIn = ({ 
  children, 
  delay = 0, 
  duration = 'normal', 
  direction = 'up',
  className = '',
  triggerOnce = true 
}) => {
  const { ref, hasBeenVisible } = useIntersectionObserver();
  
  const directionStyles = {
    up: 'translate-y-8',
    down: 'translate-y-8',
    left: 'translate-x-8',
    right: '-translate-x-8',
    none: ''
  };

  const animationClass = triggerOnce ? hasBeenVisible : true;
  
  return (
    <div
      ref={ref}
      className={`transition-all duration-${duration} ${
        animationClass
          ? 'opacity-100 translate-y-0 translate-x-0'
          : `opacity-0 ${directionStyles[direction]}`
      } ${className}`}
      style={{ 
        transitionDelay: `${delay}ms`,
        transitionTimingFunction: AnimationConfig.easing.easeOut
      }}
    >
      {children}
    </div>
  );
};

// Stagger Animation Container
export const StaggerContainer = ({ 
  children, 
  staggerDelay = 100, 
  className = '' 
}) => {
  const { ref, hasBeenVisible } = useIntersectionObserver();
  
  return (
    <div ref={ref} className={className}>
      {React.Children.map(children, (child, index) => (
        <FadeIn 
          key={index} 
          delay={index * staggerDelay}
          triggerOnce={hasBeenVisible}
        >
          {child}
        </FadeIn>
      ))}
    </div>
  );
};

// Scale Animation Component
export const ScaleIn = ({ 
  children, 
  delay = 0, 
  duration = 'normal',
  scale = 'scale-95',
  className = '' 
}) => {
  const { ref, hasBeenVisible } = useIntersectionObserver();
  
  return (
    <div
      ref={ref}
      className={`transition-all duration-${duration} ${
        hasBeenVisible
          ? 'opacity-100 scale-100'
          : `opacity-0 ${scale}`
      } ${className}`}
      style={{ 
        transitionDelay: `${delay}ms`,
        transitionTimingFunction: AnimationConfig.easing.bounce
      }}
    >
      {children}
    </div>
  );
};

// Slide In Animation Component
export const SlideIn = ({ 
  children, 
  direction = 'left',
  delay = 0,
  duration = 'normal',
  className = '' 
}) => {
  const { ref, hasBeenVisible } = useIntersectionObserver();
  
  const directionStyles = {
    left: '-translate-x-full',
    right: 'translate-x-full',
    up: '-translate-y-full',
    down: 'translate-y-full'
  };
  
  return (
    <div
      ref={ref}
      className={`transition-all duration-${duration} ${
        hasBeenVisible
          ? 'opacity-100 translate-x-0 translate-y-0'
          : `opacity-0 ${directionStyles[direction]}`
      } ${className}`}
      style={{ 
        transitionDelay: `${delay}ms`,
        transitionTimingFunction: AnimationConfig.easing.easeOut
      }}
    >
      {children}
    </div>
  );
};

// Pulse Animation Component
export const PulseAnimation = ({ 
  children, 
  intensity = 'scale-105',
  duration = 'duration-1000',
  className = '' 
}) => {
  return (
    <div className={`animate-pulse ${duration} ${className}`}>
      {children}
    </div>
  );
};

// Bounce Animation Component
export const BounceIn = ({ 
  children, 
  delay = 0,
  className = '' 
}) => {
  const { ref, hasBeenVisible } = useIntersectionObserver();
  
  return (
    <div
      ref={ref}
      className={`transition-all duration-700 ${
        hasBeenVisible
          ? 'opacity-100 scale-100'
          : 'opacity-0 scale-50'
      } ${className}`}
      style={{ 
        transitionDelay: `${delay}ms`,
        transitionTimingFunction: AnimationConfig.easing.bounce
      }}
    >
      {children}
    </div>
  );
};

// Floating Animation Component
export const FloatingElement = ({ 
  children, 
  intensity = 'hover:-translate-y-2',
  duration = 'duration-300',
  className = '' 
}) => {
  return (
    <div className={`transition-transform ${duration} ${intensity} ${className}`}>
      {children}
    </div>
  );
};

// Loading Skeleton with improved animation
export const LoadingSkeleton = ({ 
  width = 'w-full', 
  height = 'h-4', 
  className = '',
  rounded = 'rounded' 
}) => {
  return (
    <div 
      className={`${width} ${height} ${rounded} ${className} bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200 animate-pulse`}
      style={{
        backgroundSize: '200% 100%',
        animation: 'shimmer 2s infinite linear'
      }}
    />
  );
};

// Modal/Dialog Animation Component
export const ModalAnimation = ({ 
  isOpen, 
  children, 
  onClose,
  className = '' 
}) => {
  return (
    <div
      className={`fixed inset-0 z-50 flex items-center justify-center transition-all duration-300 ${
        isOpen 
          ? 'opacity-100 backdrop-blur-sm bg-black/50' 
          : 'opacity-0 pointer-events-none'
      }`}
      onClick={onClose}
    >
      <div
        className={`transition-all duration-300 ${
          isOpen 
            ? 'opacity-100 scale-100 translate-y-0' 
            : 'opacity-0 scale-95 translate-y-4'
        } ${className}`}
        style={{ 
          transitionTimingFunction: AnimationConfig.easing.spring
        }}
        onClick={(e) => e.stopPropagation()}
      >
        {children}
      </div>
    </div>
  );
};

// Smooth Scroll to Top Button
export const SmoothScrollToTop = ({ 
  showAfter = 300,
  className = '' 
}) => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const toggleVisibility = () => {
      if (window.pageYOffset > showAfter) {
        setIsVisible(true);
      } else {
        setIsVisible(false);
      }
    };

    window.addEventListener('scroll', toggleVisibility);
    return () => window.removeEventListener('scroll', toggleVisibility);
  }, [showAfter]);

  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  };

  return (
    <button
      onClick={scrollToTop}
      className={`fixed bottom-8 right-8 z-50 p-3 bg-gradient-to-r from-accent-500 to-accent-600 text-white rounded-full shadow-lg transition-all duration-300 ${
        isVisible 
          ? 'opacity-100 scale-100 translate-y-0' 
          : 'opacity-0 scale-90 translate-y-4 pointer-events-none'
      } hover:from-accent-600 hover:to-accent-700 hover:scale-110 hover:shadow-xl ${className}`}
      style={{ 
        transitionTimingFunction: AnimationConfig.easing.spring
      }}
    >
      <ChevronUp className="w-6 h-6" />
    </button>
  );
};

// Page Transition Component
export const PageTransition = ({ children, className = '' }) => {
  return (
    <div 
      className={`transition-all duration-500 ${className}`}
      style={{ 
        transitionTimingFunction: AnimationConfig.easing.easeInOut
      }}
    >
      {children}
    </div>
  );
};

// Hover Animation Wrapper
export const HoverAnimation = ({ 
  children, 
  scale = 'hover:scale-105',
  shadow = 'hover:shadow-lg',
  duration = 'duration-200',
  className = '' 
}) => {
  return (
    <div className={`transition-all ${duration} ${scale} ${shadow} ${className}`}>
      {children}
    </div>
  );
};

export default {
  AnimationConfig,
  useIntersectionObserver,
  FadeIn,
  StaggerContainer,
  ScaleIn,
  SlideIn,
  PulseAnimation,
  BounceIn,
  FloatingElement,
  LoadingSkeleton,
  ModalAnimation,
  SmoothScrollToTop,
  PageTransition,
  HoverAnimation
};
