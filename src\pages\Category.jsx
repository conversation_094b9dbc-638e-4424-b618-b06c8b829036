import React, { useState, useMemo } from 'react';
import { useParams } from 'react-router-dom';
import ProductCard from '../components/ProductCard';
import { products } from '../data/products';
import { categories } from '../data/categories';
import { ChevronRight, ChevronDown, Package } from 'lucide-react';

const Category = () => {
  const { slug, subcategory } = useParams();
  const [sortBy, setSortBy] = useState('featured');
  const [priceRange, setPriceRange] = useState('all');
  const [selectedSize, setSelectedSize] = useState('all');
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  
  const category = categories.find(cat => cat.slug === slug);
  
  // Filter products based on category and subcategory
  const categoryProducts = useMemo(() => {
    let filtered = products.filter(product => {
      // Handle "all" category - show all products
      if (slug === 'all') {
        return true;
      }
      
      // Category filtering
      const matchesCategory = 
        product.category.toLowerCase() === slug?.toLowerCase() ||
        (slug === 'new-arrivals' && product.isNew) ||
        (slug === 'sale' && product.isOnSale);
      
      if (!matchesCategory) return false;
      
      // Subcategory filtering - use the actual subcategory field from products
      if (subcategory) {
        const productSubcategory = product.subcategory?.toLowerCase();
        const targetSubcategory = subcategory.toLowerCase();
        
        // Check if the product's subcategory matches the requested subcategory
        if (productSubcategory !== targetSubcategory) {
          return false;
        }
      }
      
      // Price range filtering
      if (priceRange !== 'all') {
        const price = product.price;
        switch (priceRange) {
          case 'under-2000':
            if (price >= 2000) return false;
            break;
          case '2000-5000':
            if (price < 2000 || price > 5000) return false;
            break;
          case 'above-5000':
            if (price <= 5000) return false;
            break;
          default:
            break;
        }
      }
      
      // Size filtering
      if (selectedSize !== 'all') {
        if (!product.sizes.includes(selectedSize)) return false;
      }
      
      return true;
    });
    
    // Sorting
    switch (sortBy) {
      case 'price-low':
        filtered.sort((a, b) => a.price - b.price);
        break;
      case 'price-high':
        filtered.sort((a, b) => b.price - a.price);
        break;
      case 'rating':
        filtered.sort((a, b) => b.rating - a.rating);
        break;
      case 'newest':
        filtered.sort((a, b) => (b.isNew ? 1 : 0) - (a.isNew ? 1 : 0));
        break;
      default:
        // Featured - keep original order
        break;
    }
    
    return filtered;
  }, [slug, subcategory, sortBy, priceRange, selectedSize]);

  let categoryTitle = category ? category.name : 
    slug === 'new-arrivals' ? 'New Arrivals' : 
    slug === 'sale' ? 'Sale Items' : 
    slug === 'all' ? 'All Products' : 'Products';
    
  // Add subcategory to title if present
  if (subcategory) {
    categoryTitle = `${subcategory.charAt(0).toUpperCase() + subcategory.slice(1)} - ${categoryTitle}`;
  }

  // Get unique sizes for filtering
  const availableSizes = useMemo(() => {
    const sizes = new Set();
    products.forEach(product => {
      if (slug === 'all' || 
          product.category.toLowerCase() === slug?.toLowerCase() ||
          (slug === 'new-arrivals' && product.isNew) ||
          (slug === 'sale' && product.isOnSale)) {
        product.sizes.forEach(size => sizes.add(size));
      }
    });
    return Array.from(sizes).sort();
  }, [slug]);

  return (
    <div className="min-h-screen bg-gradient-to-br from-amber-50 via-orange-50/50 to-yellow-50/30">
      {/* Hero Section */}
      <div className="bg-gradient-to-r from-primary-400 via-primary-500 to-accent-500 text-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-4xl md:text-5xl font-bold mb-4">
            {categoryTitle}
          </h1>
          <p className="text-xl text-primary-100 mb-2">
            {categoryProducts.length} items found
          </p>
          {slug === 'sale' && (
            <p className="text-lg text-accent-200">
              ⚡ Limited time offers - Up to 50% off!
            </p>
          )}
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Breadcrumb */}
        <nav className="mb-8">
          <ol className="flex items-center space-x-2 text-sm text-secondary-500">
            <li><a href="/" className="hover:text-primary-500 transition-colors duration-200">Home</a></li>
            <li className="flex items-center">
              <ChevronRight className="w-4 h-4 mx-2" />
              <span className="text-secondary-800 font-medium">{categoryTitle}</span>
            </li>
          </ol>
        </nav>

        {/* Filters and Sort - Mobile Toggle */}
        <div className="flex flex-col lg:flex-row gap-8">
          {/* Mobile Filter Toggle */}
          <div className="lg:hidden">
            <button
              onClick={() => setIsFilterOpen(!isFilterOpen)}
              className="w-full bg-white border border-gray-300 rounded-lg px-4 py-2 flex items-center justify-between"
            >
              <span className="font-medium">Filters & Sort</span>
              <ChevronDown className={`w-5 h-5 transform transition-transform ${isFilterOpen ? 'rotate-180' : ''}`} />
            </button>
          </div>

          {/* Filters Sidebar */}
          <div className={`lg:w-64 ${isFilterOpen ? 'block' : 'hidden lg:block'}`}>
            <div className="bg-white rounded-lg shadow-sm p-6 space-y-6">
              {/* Sort By */}
              <div>
                <h3 className="font-semibold text-gray-800 mb-3">Sort By</h3>
                <select
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value)}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-pink-500 focus:border-transparent"
                >
                  <option value="featured">Featured</option>
                  <option value="newest">Newest First</option>
                  <option value="price-low">Price: Low to High</option>
                  <option value="price-high">Price: High to Low</option>
                  <option value="rating">Highest Rated</option>
                </select>
              </div>

              {/* Price Range */}
              <div>
                <h3 className="font-semibold text-gray-800 mb-3">Price Range</h3>
                <div className="space-y-2">
                  {[
                    { value: 'all', label: 'All Prices' },
                    { value: 'under-2000', label: 'Under Rs. 2,000' },
                    { value: '2000-5000', label: 'Rs. 2,000 - Rs. 5,000' },
                    { value: 'above-5000', label: 'Above Rs. 5,000' }
                  ].map((option) => (
                    <label key={option.value} className="flex items-center">
                      <input
                        type="radio"
                        name="priceRange"
                        value={option.value}
                        checked={priceRange === option.value}
                        onChange={(e) => setPriceRange(e.target.value)}
                        className="mr-2 text-pink-500 focus:ring-pink-500"
                      />
                      <span className="text-sm text-gray-700">{option.label}</span>
                    </label>
                  ))}
                </div>
              </div>

              {/* Size Filter */}
              {availableSizes.length > 0 && (
                <div>
                  <h3 className="font-semibold text-gray-800 mb-3">Size</h3>
                  <div className="grid grid-cols-3 gap-2">
                    <button
                      onClick={() => setSelectedSize('all')}
                      className={`px-3 py-2 rounded-md text-sm font-medium transition-all duration-200 ${
                        selectedSize === 'all'
                          ? 'bg-pink-500 text-white'
                          : 'bg-gray-100 text-gray-700 hover:bg-pink-100'
                      }`}
                    >
                      All
                    </button>
                    {availableSizes.map((size) => (
                      <button
                        key={size}
                        onClick={() => setSelectedSize(size)}
                        className={`px-3 py-2 rounded-md text-sm font-medium transition-all duration-200 ${
                          selectedSize === size
                            ? 'bg-pink-500 text-white'
                            : 'bg-gray-100 text-gray-700 hover:bg-pink-100'
                        }`}
                      >
                        {size}
                      </button>
                    ))}
                  </div>
                </div>
              )}

              {/* Clear Filters */}
              <button
                onClick={() => {
                  setSortBy('featured');
                  setPriceRange('all');
                  setSelectedSize('all');
                }}
                className="w-full bg-gray-100 text-gray-700 py-2 px-4 rounded-md hover:bg-gray-200 transition-colors duration-200"
              >
                Clear All Filters
              </button>
            </div>
          </div>

          {/* Products Grid */}
          <div className="flex-1">
            {/* Results Header */}
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 bg-white rounded-lg p-4 shadow-sm">
              <div>
                <h2 className="text-lg font-semibold text-gray-800">
                  {categoryProducts.length} Products Found
                </h2>
                <p className="text-sm text-gray-600">
                  {sortBy !== 'featured' && `Sorted by ${sortBy.replace('-', ' ')}`}
                </p>
              </div>
              
              {/* Quick Sort (Desktop) */}
              <div className="hidden sm:block">
                <select
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value)}
                  className="border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-pink-500 focus:border-transparent"
                >
                  <option value="featured">Featured</option>
                  <option value="newest">Newest</option>
                  <option value="price-low">Price ↑</option>
                  <option value="price-high">Price ↓</option>
                  <option value="rating">Rating</option>
                </select>
              </div>
            </div>

            {/* Products Grid */}
            {categoryProducts.length > 0 ? (
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-2 xl:grid-cols-3 gap-8">
                {categoryProducts.map((product) => (
                  <ProductCard key={product.id} product={product} />
                ))}
              </div>
            ) : (
              // No Products Message
              <div className="text-center py-16 bg-white rounded-lg shadow-sm">
                <div className="mb-8">
                  <svg className="w-24 h-24 text-gray-300 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                </div>
                <h2 className="text-2xl font-semibold text-gray-800 mb-4">
                  No products found
                </h2>
                <p className="text-gray-600 mb-8 max-w-md mx-auto">
                  We couldn't find any products matching your current filters. Try adjusting your search criteria.
                </p>
                <div className="space-y-4">
                  <button
                    onClick={() => {
                      setSortBy('featured');
                      setPriceRange('all');
                      setSelectedSize('all');
                    }}
                    className="bg-pink-500 text-white px-6 py-3 rounded-full font-semibold hover:bg-pink-600 transition-all duration-200 mr-4"
                  >
                    Clear Filters
                  </button>
                  <a
                    href="/"
                    className="bg-gray-100 text-gray-700 px-6 py-3 rounded-full font-semibold hover:bg-gray-200 transition-all duration-200"
                  >
                    Browse All Products
                  </a>
                </div>
              </div>
            )}

            {/* Load More Button (Future Enhancement) */}
            {categoryProducts.length > 0 && categoryProducts.length >= 12 && (
              <div className="text-center mt-12">
                <button className="bg-white border-2 border-pink-500 text-pink-500 px-8 py-3 rounded-full font-semibold hover:bg-pink-500 hover:text-white transition-all duration-200">
                  Load More Products
                </button>
              </div>
            )}
          </div>
        </div>

        {/* Recently Viewed (Future Enhancement) */}
        <div className="mt-16 pt-8 border-t border-gray-200">
          <h3 className="text-2xl font-bold text-gray-800 mb-6">You Might Also Like</h3>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
            {products.slice(0, 3).map((product) => (
              <ProductCard key={`related-${product.id}`} product={product} />
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Category;
