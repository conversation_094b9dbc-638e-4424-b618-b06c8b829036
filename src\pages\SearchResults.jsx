import React, { useState, useEffect } from 'react';
import { useSearchParams, Link } from 'react-router-dom';
import products from '../data/products';
import ProductCard from '../components/ProductCard';
import AdvancedFilters from '../components/AdvancedFilters';
import { Search, Filter, SortAsc, SortDesc } from 'lucide-react';

const SearchResults = () => {
  const [searchParams] = useSearchParams();
  const query = searchParams.get('q') || '';
  const [filteredProducts, setFilteredProducts] = useState([]);
  const [sortBy, setSortBy] = useState('relevance');
  const [appliedFilters, setAppliedFilters] = useState({});
  const [showFilters, setShowFilters] = useState(false);

  useEffect(() => {
    if (query) {
      const searchQuery = query.toLowerCase();
      let results = products.filter(product => 
        product.name.toLowerCase().includes(searchQuery) ||
        product.category.toLowerCase().includes(searchQuery) ||
        product.subcategory?.toLowerCase().includes(searchQuery) ||
        product.description.toLowerCase().includes(searchQuery)
      );

      // Apply filters
      if (appliedFilters.categories?.length > 0) {
        results = results.filter(product => 
          appliedFilters.categories.includes(product.category)
        );
      }

      if (appliedFilters.priceRange) {
        results = results.filter(product => 
          product.price >= appliedFilters.priceRange[0] && 
          product.price <= appliedFilters.priceRange[1]
        );
      }

      if (appliedFilters.sizes?.length > 0) {
        results = results.filter(product => 
          product.sizes?.some(size => appliedFilters.sizes.includes(size))
        );
      }

      if (appliedFilters.colors?.length > 0) {
        results = results.filter(product => 
          product.colors?.some(color => appliedFilters.colors.includes(color))
        );
      }

      if (appliedFilters.occasions?.length > 0) {
        results = results.filter(product => 
          appliedFilters.occasions.includes(product.occasion)
        );
      }

      if (appliedFilters.fabrics?.length > 0) {
        results = results.filter(product => 
          appliedFilters.fabrics.includes(product.fabric)
        );
      }

      if (appliedFilters.isOnSale) {
        results = results.filter(product => product.isOnSale);
      }

      if (appliedFilters.isNew) {
        results = results.filter(product => product.isNew);
      }

      // Apply sorting
      switch (sortBy) {
        case 'price-low':
          results.sort((a, b) => a.price - b.price);
          break;
        case 'price-high':
          results.sort((a, b) => b.price - a.price);
          break;
        case 'name':
          results.sort((a, b) => a.name.localeCompare(b.name));
          break;
        case 'newest':
          results.sort((a, b) => b.id - a.id);
          break;
        case 'rating':
          results.sort((a, b) => (b.rating || 0) - (a.rating || 0));
          break;
        default:
          // Keep original order for relevance
          break;
      }

      setFilteredProducts(results);
    } else {
      setFilteredProducts([]);
    }
  }, [query, appliedFilters, sortBy]);

  const handleFiltersChange = (newFilters) => {
    setAppliedFilters(newFilters);
  };

  if (!query) {
    return (
      <div className="min-h-screen bg-primary-50 py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center py-16">
            <Search className="w-24 h-24 text-secondary-300 mx-auto mb-4" />
            <h2 className="text-2xl font-bold text-secondary-800 mb-4">No search query</h2>
            <p className="text-secondary-600 mb-8">Please enter a search term to find products.</p>
            <Link 
              to="/" 
              className="bg-gradient-to-r from-accent-500 to-accent-600 text-white px-8 py-3 rounded-lg font-semibold hover:from-accent-600 hover:to-accent-700 transition-all duration-200"
            >
              Go to Homepage
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-primary-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Search Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-secondary-800 mb-2">
            Search Results for "{query}"
          </h1>
          <p className="text-secondary-600">
            Found {filteredProducts.length} {filteredProducts.length === 1 ? 'product' : 'products'}
          </p>
        </div>

        {/* Filters and Sort Bar */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-8">
          <div className="flex items-center gap-4">
            <AdvancedFilters 
              onFiltersChange={handleFiltersChange}
              currentFilters={appliedFilters}
            />
            <button
              onClick={() => setShowFilters(!showFilters)}
              className="lg:hidden flex items-center gap-2 px-4 py-2 bg-white border border-primary-200 rounded-lg hover:bg-primary-50 transition-colors"
            >
              <Filter className="w-4 h-4" />
              <span className="text-sm font-medium">Filters</span>
            </button>
          </div>

          {/* Sort Dropdown */}
          <div className="flex items-center gap-2">
            <span className="text-sm text-secondary-600">Sort by:</span>
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              className="px-3 py-2 border border-primary-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-accent-400 focus:border-transparent bg-white"
            >
              <option value="relevance">Relevance</option>
              <option value="newest">Newest</option>
              <option value="price-low">Price: Low to High</option>
              <option value="price-high">Price: High to Low</option>
              <option value="name">Name A-Z</option>
              <option value="rating">Rating</option>
            </select>
          </div>
        </div>

        {/* Products Grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {filteredProducts.map((product) => (
            <ProductCard key={product.id} product={product} />
          ))}
        </div>

        {/* No Results */}
        {filteredProducts.length === 0 && (
          <div className="text-center py-16">
            <Search className="w-24 h-24 text-secondary-300 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-secondary-800 mb-2">No products found</h3>
            <p className="text-secondary-600 mb-8">
              Try adjusting your search terms or filters to find what you're looking for.
            </p>
            <Link 
              to="/" 
              className="bg-gradient-to-r from-accent-500 to-accent-600 text-white px-8 py-3 rounded-lg font-semibold hover:from-accent-600 hover:to-accent-700 transition-all duration-200"
            >
              Continue Shopping
            </Link>
          </div>
        )}
      </div>
    </div>
  );
};

export default SearchResults;
