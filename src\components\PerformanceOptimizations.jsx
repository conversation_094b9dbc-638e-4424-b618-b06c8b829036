import React, { useState, useEffect, useMemo } from 'react';
import { debounce } from 'lodash';

// Lazy Image Component with loading states
export const LazyImage = ({ 
  src, 
  alt, 
  className = "", 
  loadingClassName = "bg-gray-200 animate-pulse",
  errorClassName = "bg-gray-100 flex items-center justify-center text-gray-400"
}) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [isError, setIsError] = useState(false);
  const [inView, setInView] = useState(false);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setInView(true);
          observer.disconnect();
        }
      },
      { threshold: 0.1 }
    );

    const element = document.querySelector(`[data-src="${src}"]`);
    if (element) {
      observer.observe(element);
    }

    return () => observer.disconnect();
  }, [src]);

  if (!inView) {
    return <div className={`${className} ${loadingClassName}`} data-src={src} />;
  }

  if (isError) {
    return (
      <div className={`${className} ${errorClassName}`}>
        <span className="text-sm">Failed to load</span>
      </div>
    );
  }

  return (
    <div className={className}>
      {!isLoaded && <div className={`absolute inset-0 ${loadingClassName}`} />}
      <img
        src={src}
        alt={alt}
        className={`${className} ${isLoaded ? 'opacity-100' : 'opacity-0'} transition-opacity duration-300`}
        onLoad={() => setIsLoaded(true)}
        onError={() => setIsError(true)}
      />
    </div>
  );
};

// Virtual Scroll Component for large lists
export const VirtualScroll = ({ 
  items, 
  itemHeight = 100, 
  containerHeight = 400,
  renderItem,
  overscan = 5 
}) => {
  const [scrollTop, setScrollTop] = useState(0);
  const [containerRef, setContainerRef] = useState(null);

  const visibleItems = useMemo(() => {
    const startIndex = Math.floor(scrollTop / itemHeight);
    const endIndex = Math.min(
      startIndex + Math.ceil(containerHeight / itemHeight) + overscan,
      items.length
    );
    
    return items.slice(Math.max(0, startIndex - overscan), endIndex);
  }, [items, scrollTop, itemHeight, containerHeight, overscan]);

  const handleScroll = debounce((e) => {
    setScrollTop(e.target.scrollTop);
  }, 16);

  const totalHeight = items.length * itemHeight;
  const startIndex = Math.max(0, Math.floor(scrollTop / itemHeight) - overscan);
  const offsetY = startIndex * itemHeight;

  return (
    <div
      ref={setContainerRef}
      className="overflow-auto"
      style={{ height: containerHeight }}
      onScroll={handleScroll}
    >
      <div style={{ height: totalHeight, position: 'relative' }}>
        <div style={{ transform: `translateY(${offsetY}px)` }}>
          {visibleItems.map((item, index) => (
            <div key={startIndex + index} style={{ height: itemHeight }}>
              {renderItem(item, startIndex + index)}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

// Debounced Search Hook
export const useDebounceSearch = (searchTerm, delay = 300) => {
  const [debouncedTerm, setDebouncedTerm] = useState(searchTerm);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedTerm(searchTerm);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [searchTerm, delay]);

  return debouncedTerm;
};

// Memoized Product Filter
export const useProductFilter = (products, filters, searchTerm) => {
  return useMemo(() => {
    if (!products || products.length === 0) return [];

    let filteredProducts = [...products];

    // Apply search filter
    if (searchTerm) {
      const searchLower = searchTerm.toLowerCase();
      filteredProducts = filteredProducts.filter(product => 
        product.name.toLowerCase().includes(searchLower) ||
        product.category.toLowerCase().includes(searchLower) ||
        product.subcategory?.toLowerCase().includes(searchLower) ||
        product.description.toLowerCase().includes(searchLower)
      );
    }

    // Apply category filter
    if (filters.categories?.length > 0) {
      filteredProducts = filteredProducts.filter(product => 
        filters.categories.includes(product.category)
      );
    }

    // Apply price filter
    if (filters.priceRange) {
      filteredProducts = filteredProducts.filter(product => 
        product.price >= filters.priceRange[0] && 
        product.price <= filters.priceRange[1]
      );
    }

    // Apply size filter
    if (filters.sizes?.length > 0) {
      filteredProducts = filteredProducts.filter(product => 
        product.sizes?.some(size => filters.sizes.includes(size))
      );
    }

    // Apply color filter
    if (filters.colors?.length > 0) {
      filteredProducts = filteredProducts.filter(product => 
        product.colors?.some(color => filters.colors.includes(color))
      );
    }

    // Apply boolean filters
    if (filters.isOnSale) {
      filteredProducts = filteredProducts.filter(product => product.isOnSale);
    }

    if (filters.isNew) {
      filteredProducts = filteredProducts.filter(product => product.isNew);
    }

    return filteredProducts;
  }, [products, filters, searchTerm]);
};

// Image preloader utility
export const preloadImage = (src) => {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.onload = resolve;
    img.onerror = reject;
    img.src = src;
  });
};

// Batch image preloader
export const preloadImages = (images) => {
  return Promise.allSettled(images.map(preloadImage));
};

// Cache hook for API calls
export const useCache = (key, fetcher, expiry = 5 * 60 * 1000) => {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const cached = localStorage.getItem(key);
    if (cached) {
      try {
        const { data: cachedData, timestamp } = JSON.parse(cached);
        if (Date.now() - timestamp < expiry) {
          setData(cachedData);
          setLoading(false);
          return;
        }
      } catch (e) {
        localStorage.removeItem(key);
      }
    }

    fetcher()
      .then(result => {
        setData(result);
        localStorage.setItem(key, JSON.stringify({
          data: result,
          timestamp: Date.now()
        }));
      })
      .catch(setError)
      .finally(() => setLoading(false));
  }, [key, fetcher, expiry]);

  return { data, loading, error };
};

// Intersection Observer Hook
export const useIntersectionObserver = (options = {}) => {
  const [isIntersecting, setIsIntersecting] = useState(false);
  const [ref, setRef] = useState(null);

  useEffect(() => {
    if (!ref) return;

    const observer = new IntersectionObserver(
      ([entry]) => setIsIntersecting(entry.isIntersecting),
      options
    );

    observer.observe(ref);

    return () => observer.disconnect();
  }, [ref, options]);

  return [setRef, isIntersecting];
};

// Optimized Product Grid Component
export const OptimizedProductGrid = ({ 
  products, 
  renderProduct,
  itemsPerRow = 4,
  gap = 24 
}) => {
  const [containerRef, isVisible] = useIntersectionObserver({
    threshold: 0.1,
    rootMargin: '50px'
  });

  if (!isVisible) {
    return <div ref={containerRef} className="w-full h-96 bg-gray-100 animate-pulse rounded-lg" />;
  }

  return (
    <div 
      ref={containerRef}
      className="grid"
      style={{
        gridTemplateColumns: `repeat(${itemsPerRow}, 1fr)`,
        gap: `${gap}px`
      }}
    >
      {products.map((product, index) => (
        <div key={product.id || index}>
          {renderProduct(product)}
        </div>
      ))}
    </div>
  );
};
