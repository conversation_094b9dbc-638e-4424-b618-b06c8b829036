@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  font-family: 'Poppins', sans-serif;
  
  /* Elegant Neutral Color Palette - Warm Beiges/Creams with Rose Gold */
  --primary-50: #fdfcf8;    /* Lightest cream */
  --primary-100: #faf7f0;   /* Very light cream */
  --primary-200: #f5f0e8;   /* Light cream */
  --primary-300: #ede4d3;   /* Soft beige */
  --primary-400: #e0d0b7;   /* Warm beige */
  --primary-500: #d4b896;   /* Medium beige */
  --primary-600: #c49a6b;   /* Darker beige */
  --primary-700: #b08355;   /* Deep beige */
  --primary-800: #8a6b44;   /* Rich beige */
  --primary-900: #6b5137;   /* Darkest beige */

  /* Rose Gold Accents */
  --accent-50: #fef8f6;     /* Lightest rose gold */
  --accent-100: #fdeee8;    /* Very light rose gold */
  --accent-200: #fad7c4;    /* Light rose gold */
  --accent-300: #f7b896;    /* Soft rose gold */
  --accent-400: #f19066;    /* Medium rose gold */
  --accent-500: #e8714a;    /* Rose gold primary */
  --accent-600: #d4562f;    /* Deep rose gold */
  --accent-700: #b5421f;    /* Darker rose gold */
  --accent-800: #93361a;    /* Rich rose gold */
  --accent-900: #732c18;    /* Darkest rose gold */

  /* Complementary Neutrals */
  --secondary-50: #fafafa;   /* Almost white */
  --secondary-100: #f5f5f5;  /* Light gray */
  --secondary-200: #e5e5e5;  /* Soft gray */
  --secondary-300: #d4d4d4;  /* Medium gray */
  --secondary-400: #a3a3a3;  /* Gray */
  --secondary-500: #737373;  /* Dark gray */
  --secondary-600: #525252;  /* Darker gray */
  --secondary-700: #404040;  /* Deep gray */
  --secondary-800: #262626;  /* Very dark gray */
  --secondary-900: #171717;  /* Almost black */

  /* Background and surface colors */
  --bg-primary: #faf7f0;     /* Very light cream */
  --bg-secondary: #fdfcf8;   /* Lightest cream */
  --surface: #ffffff;        /* Pure white */
  --surface-hover: #f5f0e8;  /* Light cream on hover */
}

/* Custom utility classes for elegant neutral theme */
.bg-elegant-neutral {
  background: linear-gradient(135deg, 
    rgb(254, 247, 240) 0%, 
    rgb(255, 251, 235) 25%, 
    rgb(254, 243, 199) 50%, 
    rgb(253, 230, 138) 75%, 
    rgb(254, 247, 240) 100%);
}

.bg-elegant-card {
  background: linear-gradient(145deg, 
    rgba(255, 255, 255, 0.95) 0%, 
    rgba(251, 247, 240, 0.9) 100%);
  backdrop-filter: blur(10px);
}

.shadow-elegant {
  box-shadow: 
    0 4px 6px -1px rgba(180, 83, 9, 0.1), 
    0 2px 4px -1px rgba(180, 83, 9, 0.06);
}

.shadow-elegant-lg {
  box-shadow: 
    0 10px 15px -3px rgba(180, 83, 9, 0.1), 
    0 4px 6px -2px rgba(180, 83, 9, 0.05);
}

/* Button variants */
.btn-elegant-primary {
  background: linear-gradient(135deg, var(--primary-400), var(--primary-500));
  transition: all 0.2s ease-in-out;
}

.btn-elegant-primary:hover {
  background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
  transform: translateY(-1px);
}

.btn-elegant-secondary {
  background: linear-gradient(135deg, var(--secondary-100), var(--secondary-200));
  color: var(--secondary-700);
  border: 1px solid var(--primary-200);
}

.btn-elegant-secondary:hover {
  background: linear-gradient(135deg, var(--primary-50), var(--primary-100));
  color: var(--primary-600);
}

/* Custom Animations */
@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 0 0 rgba(228, 113, 74, 0.4);
  }
  50% {
    box-shadow: 0 0 0 10px rgba(228, 113, 74, 0);
  }
}

/* Animation utility classes */
.animate-shimmer {
  animation: shimmer 2s infinite linear;
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-slide-in-up {
  animation: slideInUp 0.5s ease-out;
}

.animate-slide-in-left {
  animation: slideInLeft 0.5s ease-out;
}

.animate-slide-in-right {
  animation: slideInRight 0.5s ease-out;
}

.animate-scale-in {
  animation: scaleIn 0.3s ease-out;
}

.animate-bounce-in {
  animation: bounceIn 0.6s ease-out;
}

.animate-fade-in-up {
  animation: fadeInUp 0.4s ease-out;
}

.animate-pulse-glow {
  animation: pulse-glow 2s infinite;
}

/* Smooth transitions for better UX */
.transition-smooth {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.transition-bounce {
  transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.transition-spring {
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

/* Custom utility classes for the new color scheme */
@layer utilities {
  .text-primary {
    color: var(--primary-500);
  }
  
  .text-secondary {
    color: var(--secondary-500);
  }
  
  .text-accent {
    color: var(--accent-500);
  }
  
  .bg-primary {
    background-color: var(--primary-500);
  }
  
  .bg-primary-light {
    background-color: var(--primary-100);
  }
  
  .bg-secondary {
    background-color: var(--secondary-500);
  }
  
  .bg-accent {
    background-color: var(--accent-500);
  }
  
  .border-primary {
    border-color: var(--primary-500);
  }
  
  .border-secondary {
    border-color: var(--secondary-300);
  }
  
  .hover\:bg-primary-hover:hover {
    background-color: var(--primary-600);
  }
  
  .gradient-primary {
    background: linear-gradient(135deg, var(--primary-400), var(--primary-600));
  }
  
  .gradient-accent {
    background: linear-gradient(135deg, var(--accent-400), var(--accent-600));
  }
}
