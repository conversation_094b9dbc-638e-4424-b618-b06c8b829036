{"name": "shophub", "private": true, "version": "0.0.0", "type": "module", "homepage": "https://Qaswar-01.github.io/shopHub-E-commerce", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "vercel-build": "npm run build", "predeploy": "npm run build", "deploy": "gh-pages -d dist"}, "dependencies": {"lodash": "^4.17.21", "lucide-react": "^0.525.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-helmet-async": "^2.0.5", "react-router-dom": "^7.6.3"}, "devDependencies": {"@eslint/js": "^9.29.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.5.2", "autoprefixer": "^10.4.21", "eslint": "^9.29.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "gh-pages": "^6.3.0", "globals": "^16.2.0", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "vite": "^7.0.0"}}