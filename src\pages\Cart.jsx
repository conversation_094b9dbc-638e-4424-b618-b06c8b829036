import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { useCart } from '../context/CartContext';
import { ShoppingCart, Minus, Plus, Trash2 } from 'lucide-react';

const Cart = () => {
  const { cart, removeFromCart, updateQuantity, getCartTotal, applyPromoCode, removePromoCode } = useCart();
  const [promoCode, setPromoCode] = useState('');
  const [promoError, setPromoError] = useState('');

  const formatPrice = (price) => {
    return new Intl.NumberFormat('en-PK', {
      style: 'currency',
      currency: 'PKR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(price).replace('PKR', 'Rs.');
  };

  const handleQuantityChange = (id, selectedSize, selectedColor, newQuantity) => {
    updateQuantity(id, selectedSize, selectedColor, parseInt(newQuantity));
  };

  const handleRemoveItem = (id, selectedSize, selectedColor) => {
    removeFromCart(id, selectedSize, selectedColor);
  };

  const handleApplyPromo = () => {
    const success = applyPromoCode(promoCode);
    if (success) {
      setPromoError('');
      setPromoCode('');
    } else {
      setPromoError('Invalid promo code. Try: WELCOME10, SAVE20, FIRST15, SUMMER25');
    }
  };

  const handleRemovePromo = () => {
    removePromoCode();
  };

  const { subtotal, discount, total, itemCount } = getCartTotal();
  const cartShipping = subtotal > 3000 ? 0 : 250;
  const finalTotal = total + cartShipping;

  if (cart.items.length === 0) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-primary-50 via-primary-100/50 to-primary-200/30 py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center py-16">
            <ShoppingCart className="w-24 h-24 text-secondary-300 mx-auto mb-4" />
            <h2 className="text-2xl font-bold text-secondary-800 mb-4">Your cart is empty</h2>
            <p className="text-secondary-600 mb-8">Looks like you haven't added anything to your cart yet.</p>
            <Link 
              to="/" 
              className="bg-gradient-to-r from-accent-500 to-accent-600 text-white px-8 py-3 rounded-lg font-semibold hover:from-accent-600 hover:to-accent-700 transition-all duration-200"
            >
              Continue Shopping
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-primary-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between mb-8">
          <h1 className="text-3xl font-bold text-secondary-800">Shopping Cart</h1>
          <Link to="/" className="text-accent-500 hover:text-accent-600 font-medium">
            Continue Shopping
          </Link>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Cart Items */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-lg shadow-md overflow-hidden">
              <div className="px-6 py-4 border-b border-secondary-200">
                <h2 className="text-xl font-semibold text-secondary-800">
                  Cart Items ({itemCount} {itemCount === 1 ? 'item' : 'items'})
                </h2>
              </div>

              <div className="divide-y divide-secondary-200">
                {cart.items.map((item, index) => (
                  <div key={`${item.id}-${item.selectedSize}-${item.selectedColor}`} className="p-6">
                    <div className="flex items-start space-x-4">
                      {/* Product Image */}
                      <div className="flex-shrink-0">
                        <img
                          src={item.image}
                          alt={item.name}
                          className="w-20 h-20 object-cover rounded-lg"
                        />
                      </div>

                      {/* Product Details */}
                      <div className="flex-1 min-w-0">
                        <h3 className="text-lg font-medium text-gray-800 mb-1">
                          <Link to={`/product/${item.id}`} className="hover:text-pink-500">
                            {item.name}
                          </Link>
                        </h3>
                        <p className="text-sm text-gray-500 mb-2">{item.category}</p>
                        
                        <div className="flex items-center space-x-4 text-sm text-gray-600 mb-3">
                          {item.selectedSize && (
                            <span>Size: <span className="font-medium">{item.selectedSize}</span></span>
                          )}
                          {item.selectedColor && (
                            <span>Color: <span className="font-medium">{item.selectedColor}</span></span>
                          )}
                        </div>

                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-2">
                            <span className="text-lg font-bold text-gray-800">
                              {formatPrice(item.price)}
                            </span>
                            {item.isOnSale && item.originalPrice && (
                              <span className="text-sm text-gray-500 line-through">
                                {formatPrice(item.originalPrice)}
                              </span>
                            )}
                          </div>

                          <div className="flex items-center space-x-3">
                            {/* Quantity Controls */}
                            <div className="flex items-center border border-gray-300 rounded-lg">
                              <button
                                onClick={() => handleQuantityChange(item.id, item.selectedSize, item.selectedColor, item.quantity - 1)}
                                className="px-3 py-1 text-gray-600 hover:text-gray-800"
                                disabled={item.quantity <= 1}
                              >
                                -
                              </button>
                              <span className="px-3 py-1 border-x border-gray-300 min-w-[3rem] text-center">
                                {item.quantity}
                              </span>
                              <button
                                onClick={() => handleQuantityChange(item.id, item.selectedSize, item.selectedColor, item.quantity + 1)}
                                className="px-3 py-1 text-gray-600 hover:text-gray-800"
                              >
                                +
                              </button>
                            </div>

                            {/* Remove Button */}
                            <button
                              onClick={() => handleRemoveItem(item.id, item.selectedSize, item.selectedColor)}
                              className="text-red-500 hover:text-red-700 p-1"
                              title="Remove item"
                            >
                              <Trash2 className="w-5 h-5" />
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Order Summary */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow-md p-6 sticky top-24">
              <h2 className="text-xl font-semibold text-gray-800 mb-6">Order Summary</h2>

              {/* Promo Code */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Promo Code
                </label>
                {cart.promoCode ? (
                  <div className="flex items-center justify-between p-3 bg-green-50 border border-green-200 rounded-lg">
                    <span className="text-green-800 font-medium">{cart.promoCode}</span>
                    <button
                      onClick={handleRemovePromo}
                      className="text-green-600 hover:text-green-800"
                    >
                      Remove
                    </button>
                  </div>
                ) : (
                  <div className="space-y-2">
                    <div className="flex space-x-2">
                      <input
                        type="text"
                        value={promoCode}
                        onChange={(e) => setPromoCode(e.target.value.toUpperCase())}
                        placeholder="Enter promo code"
                        className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-pink-500"
                      />
                      <button
                        onClick={handleApplyPromo}
                        className="px-4 py-2 bg-gray-800 text-white rounded-lg hover:bg-gray-900 transition-colors"
                      >
                        Apply
                      </button>
                    </div>
                    {promoError && (
                      <p className="text-sm text-red-600">{promoError}</p>
                    )}
                  </div>
                )}
              </div>

              {/* Price Breakdown */}
              <div className="space-y-3 mb-6">
                <div className="flex justify-between text-gray-600">
                  <span>Subtotal ({itemCount} items)</span>
                  <span>{formatPrice(subtotal)}</span>
                </div>
                {cart.promoCode && (
                  <div className="flex justify-between text-green-600">
                    <span>Discount ({cart.discount}%)</span>
                    <span>-{formatPrice(discount)}</span>
                  </div>
                )}
                <div className="flex justify-between text-gray-600">
                  <span>Shipping</span>
                  <span>{cartShipping === 0 ? 'Free' : formatPrice(cartShipping)}</span>
                </div>
                <div className="border-t border-gray-200 pt-3">
                  <div className="flex justify-between text-lg font-bold text-gray-800">
                    <span>Total</span>
                    <span>{formatPrice(finalTotal)}</span>
                  </div>
                </div>
              </div>

              {/* Free Shipping Notice */}
              {subtotal < 3000 && (
                <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                  <p className="text-sm text-blue-800">
                    Add {formatPrice(3000 - subtotal)} more for free shipping!
                  </p>
                </div>
              )}

              {/* Checkout Button */}
              <button className="w-full bg-gradient-to-r from-pink-500 to-purple-500 text-white py-3 rounded-lg font-semibold hover:from-pink-600 hover:to-purple-600 transition-all duration-200 transform hover:scale-105 mb-4">
                Proceed to Checkout
              </button>

              {/* Payment Methods */}
              <div className="text-center">
                <p className="text-sm text-gray-600 mb-2">We accept</p>
                <div className="flex justify-center space-x-2">
                  <div className="w-8 h-5 bg-blue-600 rounded text-white text-xs flex items-center justify-center font-bold">
                    VISA
                  </div>
                  <div className="w-8 h-5 bg-red-500 rounded text-white text-xs flex items-center justify-center font-bold">
                    MC
                  </div>
                  <div className="w-8 h-5 bg-purple-600 rounded text-white text-xs flex items-center justify-center font-bold">
                    COD
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Cart;
