import React, { useState } from 'react';
import { Filter, X, ChevronDown, ChevronUp } from 'lucide-react';

const AdvancedFilters = ({ onFiltersChange, currentFilters = {} }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [expandedSections, setExpandedSections] = useState({
    category: true,
    price: true,
    size: false,
    color: false,
    occasion: false,
    fabric: false
  });

  const [filters, setFilters] = useState({
    categories: currentFilters.categories || [],
    priceRange: currentFilters.priceRange || [0, 20000],
    sizes: currentFilters.sizes || [],
    colors: currentFilters.colors || [],
    occasions: currentFilters.occasions || [],
    fabrics: currentFilters.fabrics || [],
    isOnSale: currentFilters.isOnSale || false,
    isNew: currentFilters.isNew || false,
    ...currentFilters
  });

  const categories = ['Eastern', 'Western', 'Accessories', 'Footwear'];
  const sizes = ['XS', 'S', 'M', 'L', 'XL', 'XXL'];
  const colors = ['Black', 'White', 'Red', 'Blue', 'Green', 'Pink', 'Purple', 'Yellow', 'Orange', 'Brown', 'Gray'];
  const occasions = ['Casual', 'Formal', 'Party', 'Wedding', 'Work', 'Festival'];
  const fabrics = ['Cotton', 'Silk', 'Chiffon', 'Lawn', 'Linen', 'Polyester', 'Velvet', 'Organza'];

  const toggleSection = (section) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  const handleCategoryChange = (category) => {
    const newCategories = filters.categories.includes(category)
      ? filters.categories.filter(c => c !== category)
      : [...filters.categories, category];
    
    const newFilters = { ...filters, categories: newCategories };
    setFilters(newFilters);
    onFiltersChange(newFilters);
  };

  const handleArrayFilterChange = (filterType, value) => {
    const currentArray = filters[filterType] || [];
    const newArray = currentArray.includes(value)
      ? currentArray.filter(item => item !== value)
      : [...currentArray, value];
    
    const newFilters = { ...filters, [filterType]: newArray };
    setFilters(newFilters);
    onFiltersChange(newFilters);
  };

  const handlePriceChange = (minPrice, maxPrice) => {
    const newFilters = { ...filters, priceRange: [minPrice, maxPrice] };
    setFilters(newFilters);
    onFiltersChange(newFilters);
  };

  const handleBooleanFilterChange = (filterType) => {
    const newFilters = { ...filters, [filterType]: !filters[filterType] };
    setFilters(newFilters);
    onFiltersChange(newFilters);
  };

  const clearAllFilters = () => {
    const clearedFilters = {
      categories: [],
      priceRange: [0, 20000],
      sizes: [],
      colors: [],
      occasions: [],
      fabrics: [],
      isOnSale: false,
      isNew: false
    };
    setFilters(clearedFilters);
    onFiltersChange(clearedFilters);
  };

  const getActiveFiltersCount = () => {
    return (
      filters.categories.length +
      filters.sizes.length +
      filters.colors.length +
      filters.occasions.length +
      filters.fabrics.length +
      (filters.isOnSale ? 1 : 0) +
      (filters.isNew ? 1 : 0) +
      (filters.priceRange[0] !== 0 || filters.priceRange[1] !== 20000 ? 1 : 0)
    );
  };

  const FilterSection = ({ title, isExpanded, onToggle, children }) => (
    <div className="border-b border-primary-200 pb-4 mb-4">
      <button
        onClick={onToggle}
        className="flex items-center justify-between w-full text-left"
      >
        <h3 className="text-sm font-semibold text-secondary-800">{title}</h3>
        {isExpanded ? <ChevronUp className="w-4 h-4" /> : <ChevronDown className="w-4 h-4" />}
      </button>
      {isExpanded && <div className="mt-3">{children}</div>}
    </div>
  );

  return (
    <div className="relative">
      {/* Filter Toggle Button */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center gap-2 px-4 py-2 bg-white border border-primary-200 rounded-lg hover:bg-primary-50 transition-colors"
      >
        <Filter className="w-4 h-4" />
        <span className="text-sm font-medium">Filters</span>
        {getActiveFiltersCount() > 0 && (
          <span className="bg-accent-500 text-white text-xs font-bold px-2 py-1 rounded-full">
            {getActiveFiltersCount()}
          </span>
        )}
      </button>

      {/* Filter Panel */}
      {isOpen && (
        <div className="absolute top-full left-0 mt-2 w-80 bg-white border border-primary-200 rounded-xl shadow-lg z-50 max-h-96 overflow-y-auto">
          <div className="p-4">
            {/* Header */}
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-lg font-semibold text-secondary-800">Filters</h2>
              <div className="flex items-center gap-2">
                <button
                  onClick={clearAllFilters}
                  className="text-sm text-secondary-500 hover:text-secondary-700"
                >
                  Clear All
                </button>
                <button
                  onClick={() => setIsOpen(false)}
                  className="p-1 hover:bg-primary-100 rounded-full"
                >
                  <X className="w-4 h-4" />
                </button>
              </div>
            </div>

            {/* Categories */}
            <FilterSection
              title="Categories"
              isExpanded={expandedSections.category}
              onToggle={() => toggleSection('category')}
            >
              <div className="space-y-2">
                {categories.map(category => (
                  <label key={category} className="flex items-center gap-2 cursor-pointer">
                    <input
                      type="checkbox"
                      checked={filters.categories.includes(category)}
                      onChange={() => handleCategoryChange(category)}
                      className="w-4 h-4 text-accent-500 border-primary-300 rounded focus:ring-accent-400"
                    />
                    <span className="text-sm text-secondary-700">{category}</span>
                  </label>
                ))}
              </div>
            </FilterSection>

            {/* Price Range */}
            <FilterSection
              title="Price Range"
              isExpanded={expandedSections.price}
              onToggle={() => toggleSection('price')}
            >
              <div className="space-y-3">
                <div className="flex items-center gap-2">
                  <span className="text-sm text-secondary-600">Rs.</span>
                  <input
                    type="number"
                    value={filters.priceRange[0]}
                    onChange={(e) => handlePriceChange(parseInt(e.target.value) || 0, filters.priceRange[1])}
                    className="w-20 px-2 py-1 border border-primary-200 rounded text-sm"
                    placeholder="Min"
                  />
                  <span className="text-sm text-secondary-600">to</span>
                  <input
                    type="number"
                    value={filters.priceRange[1]}
                    onChange={(e) => handlePriceChange(filters.priceRange[0], parseInt(e.target.value) || 20000)}
                    className="w-20 px-2 py-1 border border-primary-200 rounded text-sm"
                    placeholder="Max"
                  />
                </div>
                <div className="flex gap-2">
                  {[1000, 2000, 5000, 10000].map(price => (
                    <button
                      key={price}
                      onClick={() => handlePriceChange(0, price)}
                      className="px-2 py-1 text-xs bg-primary-100 text-secondary-600 rounded hover:bg-primary-200 transition-colors"
                    >
                      Under Rs. {price.toLocaleString()}
                    </button>
                  ))}
                </div>
              </div>
            </FilterSection>

            {/* Sizes */}
            <FilterSection
              title="Sizes"
              isExpanded={expandedSections.size}
              onToggle={() => toggleSection('size')}
            >
              <div className="flex flex-wrap gap-2">
                {sizes.map(size => (
                  <button
                    key={size}
                    onClick={() => handleArrayFilterChange('sizes', size)}
                    className={`px-3 py-1 text-sm border rounded-lg transition-colors ${
                      filters.sizes.includes(size)
                        ? 'bg-accent-500 text-white border-accent-500'
                        : 'bg-white text-secondary-700 border-primary-200 hover:border-primary-300'
                    }`}
                  >
                    {size}
                  </button>
                ))}
              </div>
            </FilterSection>

            {/* Colors */}
            <FilterSection
              title="Colors"
              isExpanded={expandedSections.color}
              onToggle={() => toggleSection('color')}
            >
              <div className="flex flex-wrap gap-2">
                {colors.map(color => (
                  <button
                    key={color}
                    onClick={() => handleArrayFilterChange('colors', color)}
                    className={`px-3 py-1 text-sm border rounded-lg transition-colors ${
                      filters.colors.includes(color)
                        ? 'bg-accent-500 text-white border-accent-500'
                        : 'bg-white text-secondary-700 border-primary-200 hover:border-primary-300'
                    }`}
                  >
                    {color}
                  </button>
                ))}
              </div>
            </FilterSection>

            {/* Occasions */}
            <FilterSection
              title="Occasions"
              isExpanded={expandedSections.occasion}
              onToggle={() => toggleSection('occasion')}
            >
              <div className="space-y-2">
                {occasions.map(occasion => (
                  <label key={occasion} className="flex items-center gap-2 cursor-pointer">
                    <input
                      type="checkbox"
                      checked={filters.occasions.includes(occasion)}
                      onChange={() => handleArrayFilterChange('occasions', occasion)}
                      className="w-4 h-4 text-accent-500 border-primary-300 rounded focus:ring-accent-400"
                    />
                    <span className="text-sm text-secondary-700">{occasion}</span>
                  </label>
                ))}
              </div>
            </FilterSection>

            {/* Fabrics */}
            <FilterSection
              title="Fabrics"
              isExpanded={expandedSections.fabric}
              onToggle={() => toggleSection('fabric')}
            >
              <div className="space-y-2">
                {fabrics.map(fabric => (
                  <label key={fabric} className="flex items-center gap-2 cursor-pointer">
                    <input
                      type="checkbox"
                      checked={filters.fabrics.includes(fabric)}
                      onChange={() => handleArrayFilterChange('fabrics', fabric)}
                      className="w-4 h-4 text-accent-500 border-primary-300 rounded focus:ring-accent-400"
                    />
                    <span className="text-sm text-secondary-700">{fabric}</span>
                  </label>
                ))}
              </div>
            </FilterSection>

            {/* Special Filters */}
            <div className="space-y-2">
              <label className="flex items-center gap-2 cursor-pointer">
                <input
                  type="checkbox"
                  checked={filters.isOnSale}
                  onChange={() => handleBooleanFilterChange('isOnSale')}
                  className="w-4 h-4 text-accent-500 border-primary-300 rounded focus:ring-accent-400"
                />
                <span className="text-sm text-secondary-700">On Sale</span>
              </label>
              <label className="flex items-center gap-2 cursor-pointer">
                <input
                  type="checkbox"
                  checked={filters.isNew}
                  onChange={() => handleBooleanFilterChange('isNew')}
                  className="w-4 h-4 text-accent-500 border-primary-300 rounded focus:ring-accent-400"
                />
                <span className="text-sm text-secondary-700">New Arrivals</span>
              </label>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AdvancedFilters;
