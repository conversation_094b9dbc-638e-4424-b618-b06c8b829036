import React, { useState, useEffect } from 'react';
import { Star, Heart, Eye, ShoppingCart, TrendingUp, Users, Clock } from 'lucide-react';
import ProductCard from './ProductCard';
import { useCart } from '../context/CartContext';
import { useWishlist } from '../context/WishlistContext';

const ProductRecommendations = ({ 
  currentProduct, 
  allProducts, 
  viewedProducts = [],
  title = "You Might Also Like",
  maxItems = 4 
}) => {
  const [recommendations, setRecommendations] = useState([]);
  const [activeTab, setActiveTab] = useState('similar');
  const { addToCart } = useCart();
  const { toggleWishlist, isInWishlist } = useWishlist();

  useEffect(() => {
    generateRecommendations();
  }, [currentProduct, allProducts, viewedProducts, activeTab]);

  const generateRecommendations = () => {
    if (!currentProduct || !allProducts) return;

    let recommended = [];

    switch (activeTab) {
      case 'similar':
        recommended = getSimilarProducts();
        break;
      case 'viewed':
        recommended = getFrequentlyViewedTogether();
        break;
      case 'trending':
        recommended = getTrendingProducts();
        break;
      case 'complete':
        recommended = getCompleteTheLook();
        break;
      default:
        recommended = getSimilarProducts();
    }

    setRecommendations(recommended.slice(0, maxItems));
  };

  const getSimilarProducts = () => {
    return allProducts
      .filter(product => {
        if (product.id === currentProduct.id) return false;
        
        // Similar category
        if (product.category === currentProduct.category) return true;
        
        // Similar price range (±30%)
        const priceDiff = Math.abs(product.price - currentProduct.price) / currentProduct.price;
        if (priceDiff <= 0.3) return true;
        
        // Similar colors
        const hasCommonColors = product.colors?.some(color => 
          currentProduct.colors?.includes(color)
        );
        if (hasCommonColors) return true;
        
        return false;
      })
      .sort((a, b) => {
        // Prioritize same category
        if (a.category === currentProduct.category && b.category !== currentProduct.category) return -1;
        if (b.category === currentProduct.category && a.category !== currentProduct.category) return 1;
        
        // Then by rating
        return (b.rating || 0) - (a.rating || 0);
      });
  };

  const getFrequentlyViewedTogether = () => {
    const viewedProductIds = viewedProducts.map(p => p.id);
    
    return allProducts
      .filter(product => {
        if (product.id === currentProduct.id) return false;
        // Products that were viewed in the same session
        return viewedProductIds.includes(product.id);
      })
      .sort((a, b) => (b.rating || 0) - (a.rating || 0));
  };

  const getTrendingProducts = () => {
    return allProducts
      .filter(product => product.id !== currentProduct.id)
      .sort((a, b) => {
        // Prioritize new products
        if (a.isNew && !b.isNew) return -1;
        if (b.isNew && !a.isNew) return 1;
        
        // Then by rating and review count
        const aScore = (a.rating || 0) * (a.reviews || 1);
        const bScore = (b.rating || 0) * (b.reviews || 1);
        return bScore - aScore;
      });
  };

  const getCompleteTheLook = () => {
    const accessories = allProducts.filter(product => 
      product.category === 'Accessories' && product.id !== currentProduct.id
    );
    
    const complementaryItems = allProducts.filter(product => {
      if (product.id === currentProduct.id) return false;
      
      // If current is Eastern, suggest other Eastern items
      if (currentProduct.category === 'Eastern' && product.category === 'Eastern') {
        return product.subcategory !== currentProduct.subcategory;
      }
      
      // If current is Western, suggest other Western items
      if (currentProduct.category === 'Western' && product.category === 'Western') {
        return product.subcategory !== currentProduct.subcategory;
      }
      
      return false;
    });
    
    return [...complementaryItems, ...accessories]
      .sort((a, b) => (b.rating || 0) - (a.rating || 0));
  };

  const formatPrice = (price) => {
    return new Intl.NumberFormat('en-PK', {
      style: 'currency',
      currency: 'PKR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(price).replace('PKR', 'Rs.');
  };

  if (recommendations.length === 0) return null;

  return (
    <div className="bg-white rounded-lg shadow-md p-6 mb-8">
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-secondary-800 mb-4">{title}</h2>
        
        {/* Recommendation Tabs */}
        <div className="flex flex-wrap gap-2">
          <button
            onClick={() => setActiveTab('similar')}
            className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
              activeTab === 'similar'
                ? 'bg-accent-500 text-white'
                : 'bg-primary-100 text-secondary-700 hover:bg-primary-200'
            }`}
          >
            <Heart className="w-4 h-4 inline mr-1" />
            Similar Items
          </button>
          <button
            onClick={() => setActiveTab('viewed')}
            className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
              activeTab === 'viewed'
                ? 'bg-accent-500 text-white'
                : 'bg-primary-100 text-secondary-700 hover:bg-primary-200'
            }`}
          >
            <Eye className="w-4 h-4 inline mr-1" />
            Recently Viewed
          </button>
          <button
            onClick={() => setActiveTab('trending')}
            className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
              activeTab === 'trending'
                ? 'bg-accent-500 text-white'
                : 'bg-primary-100 text-secondary-700 hover:bg-primary-200'
            }`}
          >
            <TrendingUp className="w-4 h-4 inline mr-1" />
            Trending
          </button>
          <button
            onClick={() => setActiveTab('complete')}
            className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
              activeTab === 'complete'
                ? 'bg-accent-500 text-white'
                : 'bg-primary-100 text-secondary-700 hover:bg-primary-200'
            }`}
          >
            <Users className="w-4 h-4 inline mr-1" />
            Complete the Look
          </button>
        </div>
      </div>

      {/* Recommendations Grid */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
        {recommendations.map((product) => (
          <div key={product.id} className="group">
            <ProductCard product={product} />
          </div>
        ))}
      </div>

      {/* Additional Recommendations Hint */}
      {recommendations.length >= maxItems && (
        <div className="mt-6 text-center">
          <p className="text-sm text-secondary-600 mb-3">
            Want to see more recommendations?
          </p>
          <button 
            onClick={() => setMaxItems(prev => prev + 4)}
            className="text-accent-600 hover:text-accent-700 font-medium text-sm"
          >
            Load More Suggestions
          </button>
        </div>
      )}
    </div>
  );
};

// Recently Viewed Products Component
export const RecentlyViewed = ({ viewedProducts, maxItems = 6 }) => {
  const [showAll, setShowAll] = useState(false);
  
  if (!viewedProducts || viewedProducts.length === 0) return null;

  const displayProducts = showAll ? viewedProducts : viewedProducts.slice(0, maxItems);

  return (
    <div className="bg-white rounded-lg shadow-md p-6 mb-8">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-2xl font-bold text-secondary-800 flex items-center gap-2">
          <Clock className="w-6 h-6" />
          Recently Viewed
        </h2>
        {viewedProducts.length > maxItems && (
          <button
            onClick={() => setShowAll(!showAll)}
            className="text-accent-600 hover:text-accent-700 font-medium text-sm"
          >
            {showAll ? 'Show Less' : `View All (${viewedProducts.length})`}
          </button>
        )}
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4">
        {displayProducts.map((product) => (
          <div key={product.id} className="group">
            <ProductCard product={product} />
          </div>
        ))}
      </div>
    </div>
  );
};

// Personalized Bundles Component
export const PersonalizedBundles = ({ 
  currentProduct, 
  allProducts, 
  userPreferences = {} 
}) => {
  const [bundles, setBundles] = useState([]);

  useEffect(() => {
    generateBundles();
  }, [currentProduct, allProducts, userPreferences]);

  const generateBundles = () => {
    if (!currentProduct || !allProducts) return;

    const complementaryProducts = allProducts.filter(product => {
      if (product.id === currentProduct.id) return false;
      
      // Create outfit bundles
      if (currentProduct.category === 'Eastern') {
        return product.category === 'Accessories' || 
               (product.category === 'Eastern' && product.subcategory !== currentProduct.subcategory);
      }
      
      if (currentProduct.category === 'Western') {
        return product.category === 'Accessories' || 
               (product.category === 'Western' && product.subcategory !== currentProduct.subcategory);
      }
      
      return false;
    });

    // Create bundles of 2-3 items
    const createdBundles = [];
    for (let i = 0; i < Math.min(3, complementaryProducts.length); i++) {
      const bundle = {
        id: `bundle-${i}`,
        items: [currentProduct, complementaryProducts[i]],
        totalPrice: currentProduct.price + complementaryProducts[i].price,
        savings: Math.round((currentProduct.price + complementaryProducts[i].price) * 0.1),
        name: `${currentProduct.name} + ${complementaryProducts[i].name}`
      };
      createdBundles.push(bundle);
    }

    setBundles(createdBundles);
  };

  const formatPrice = (price) => {
    return new Intl.NumberFormat('en-PK', {
      style: 'currency',
      currency: 'PKR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(price).replace('PKR', 'Rs.');
  };

  if (bundles.length === 0) return null;

  return (
    <div className="bg-white rounded-lg shadow-md p-6 mb-8">
      <h2 className="text-2xl font-bold text-secondary-800 mb-6">
        Bundle & Save
      </h2>

      <div className="space-y-4">
        {bundles.map((bundle) => (
          <div key={bundle.id} className="border border-primary-200 rounded-lg p-4 hover:shadow-md transition-shadow">
            <div className="flex items-center justify-between mb-4">
              <h3 className="font-semibold text-secondary-800">{bundle.name}</h3>
              <div className="text-right">
                <div className="text-lg font-bold text-secondary-800">
                  {formatPrice(bundle.totalPrice - bundle.savings)}
                </div>
                <div className="text-sm text-secondary-500 line-through">
                  {formatPrice(bundle.totalPrice)}
                </div>
                <div className="text-sm text-green-600 font-medium">
                  Save {formatPrice(bundle.savings)}
                </div>
              </div>
            </div>

            <div className="flex items-center gap-4 mb-4">
              {bundle.items.map((item, index) => (
                <React.Fragment key={item.id}>
                  <div className="flex items-center gap-2">
                    <img
                      src={item.image}
                      alt={item.name}
                      className="w-16 h-16 object-cover rounded-lg"
                    />
                    <div>
                      <p className="text-sm font-medium text-secondary-800">{item.name}</p>
                      <p className="text-sm text-secondary-600">{formatPrice(item.price)}</p>
                    </div>
                  </div>
                  {index < bundle.items.length - 1 && (
                    <div className="text-secondary-400 text-xl">+</div>
                  )}
                </React.Fragment>
              ))}
            </div>

            <button className="w-full bg-gradient-to-r from-accent-500 to-accent-600 text-white py-2 px-4 rounded-lg hover:from-accent-600 hover:to-accent-700 transition-all duration-200 transform hover:scale-105 font-semibold shadow-lg">
              Add Bundle to Cart
            </button>
          </div>
        ))}
      </div>
    </div>
  );
};

export default ProductRecommendations;
