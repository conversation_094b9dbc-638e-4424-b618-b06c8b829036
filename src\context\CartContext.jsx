import React, { createContext, useContext, useReducer, useEffect } from 'react';

const CartContext = createContext();

const cartReducer = (state, action) => {
  switch (action.type) {
    case 'ADD_TO_CART':
      const existingItem = state.items.find(
        item => item.id === action.payload.id && 
        item.selectedSize === action.payload.selectedSize && 
        item.selectedColor === action.payload.selectedColor
      );

      if (existingItem) {
        return {
          ...state,
          items: state.items.map(item =>
            item.id === action.payload.id && 
            item.selectedSize === action.payload.selectedSize && 
            item.selectedColor === action.payload.selectedColor
              ? { ...item, quantity: item.quantity + action.payload.quantity }
              : item
          )
        };
      } else {
        return {
          ...state,
          items: [...state.items, action.payload]
        };
      }

    case 'REMOVE_FROM_CART':
      return {
        ...state,
        items: state.items.filter(
          item => !(item.id === action.payload.id && 
          item.selectedSize === action.payload.selectedSize && 
          item.selectedColor === action.payload.selectedColor)
        )
      };

    case 'UPDATE_QUANTITY':
      return {
        ...state,
        items: state.items.map(item =>
          item.id === action.payload.id && 
          item.selectedSize === action.payload.selectedSize && 
          item.selectedColor === action.payload.selectedColor
            ? { ...item, quantity: action.payload.quantity }
            : item
        )
      };

    case 'CLEAR_CART':
      return {
        ...state,
        items: []
      };

    case 'APPLY_PROMO_CODE':
      return {
        ...state,
        promoCode: action.payload.code,
        discount: action.payload.discount
      };

    case 'REMOVE_PROMO_CODE':
      return {
        ...state,
        promoCode: null,
        discount: 0
      };

    default:
      return state;
  }
};

const initialState = {
  items: [],
  promoCode: null,
  discount: 0
};

export const CartProvider = ({ children }) => {
  const [state, dispatch] = useReducer(cartReducer, initialState);

  // Load cart from localStorage on mount
  useEffect(() => {
    const savedCart = localStorage.getItem('shophub-cart');
    if (savedCart) {
      const parsedCart = JSON.parse(savedCart);
      parsedCart.items.forEach(item => {
        dispatch({ type: 'ADD_TO_CART', payload: item });
      });
      if (parsedCart.promoCode) {
        dispatch({ 
          type: 'APPLY_PROMO_CODE', 
          payload: { code: parsedCart.promoCode, discount: parsedCart.discount } 
        });
      }
    }
  }, []);

  // Save cart to localStorage whenever it changes
  useEffect(() => {
    localStorage.setItem('shophub-cart', JSON.stringify(state));
  }, [state]);

  const addToCart = (product, quantity = 1, selectedSize = '', selectedColor = '') => {
    dispatch({
      type: 'ADD_TO_CART',
      payload: {
        id: product.id,
        name: product.name,
        price: product.price,
        originalPrice: product.originalPrice,
        image: product.image,
        category: product.category,
        selectedSize,
        selectedColor,
        quantity,
        isOnSale: product.isOnSale
      }
    });
  };

  const removeFromCart = (id, selectedSize, selectedColor) => {
    dispatch({
      type: 'REMOVE_FROM_CART',
      payload: { id, selectedSize, selectedColor }
    });
  };

  const updateQuantity = (id, selectedSize, selectedColor, quantity) => {
    if (quantity <= 0) {
      removeFromCart(id, selectedSize, selectedColor);
    } else {
      dispatch({
        type: 'UPDATE_QUANTITY',
        payload: { id, selectedSize, selectedColor, quantity }
      });
    }
  };

  const clearCart = () => {
    dispatch({ type: 'CLEAR_CART' });
  };

  const applyPromoCode = (code) => {
    // Promo code logic
    const promoCodes = {
      'WELCOME10': 10,
      'SAVE20': 20,
      'FIRST15': 15,
      'SUMMER25': 25
    };

    if (promoCodes[code]) {
      dispatch({
        type: 'APPLY_PROMO_CODE',
        payload: { code, discount: promoCodes[code] }
      });
      return true;
    }
    return false;
  };

  const removePromoCode = () => {
    dispatch({ type: 'REMOVE_PROMO_CODE' });
  };

  const getCartTotal = () => {
    const subtotal = state.items.reduce((total, item) => total + (item.price * item.quantity), 0);
    const discountAmount = (subtotal * state.discount) / 100;
    return {
      subtotal,
      discount: discountAmount,
      total: subtotal - discountAmount,
      itemCount: state.items.reduce((count, item) => count + item.quantity, 0)
    };
  };

  const value = {
    cart: state,
    addToCart,
    removeFromCart,
    updateQuantity,
    clearCart,
    applyPromoCode,
    removePromoCode,
    getCartTotal
  };

  return (
    <CartContext.Provider value={value}>
      {children}
    </CartContext.Provider>
  );
};

export const useCart = () => {
  const context = useContext(CartContext);
  if (!context) {
    throw new Error('useCart must be used within a CartProvider');
  }
  return context;
};

export default CartContext;
