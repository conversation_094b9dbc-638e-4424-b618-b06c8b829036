import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { Menu, X, ShoppingCart, Heart, ChevronDown, Search, User, Truck } from 'lucide-react';
import { useCart } from '../context/CartContext';
import { useWishlist } from '../context/WishlistContext';
import { useSearch } from '../context/SearchContext';
import { categories } from '../data/categories';
import products from '../data/products';
import AdvancedSearchBar from './AdvancedSearchBar';

const Navbar = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [activeDropdown, setActiveDropdown] = useState(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [showSearchResults, setShowSearchResults] = useState(false);
  const { getCartTotal } = useCart();
  const { wishlistItems } = useWishlist();
  const { addToSearchHistory, searchHistory } = useSearch();
  const { itemCount } = getCartTotal();
  const navigate = useNavigate();

  const handleDropdownToggle = (categoryId) => {
    setActiveDropdown(activeDropdown === categoryId ? null : categoryId);
  };

  // Search functionality
  const handleSearchChange = (e) => {
    const query = e.target.value;
    setSearchQuery(query);
    setShowSearchResults(query.length > 0);
  };

  const handleSearchSubmit = (e) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      addToSearchHistory(searchQuery.trim());
      navigate(`/search?q=${encodeURIComponent(searchQuery.trim())}`);
      setSearchQuery('');
      setShowSearchResults(false);
    }
  };

  const handleSearchResultClick = (productId) => {
    navigate(`/product/${productId}`);
    setSearchQuery('');
    setShowSearchResults(false);
  };

  const handleHistoryClick = (query) => {
    setSearchQuery(query);
    navigate(`/search?q=${encodeURIComponent(query)}`);
    setShowSearchResults(false);
  };

  // Filter products based on search query
  const getSearchResults = () => {
    if (!searchQuery.trim()) return [];
    
    const query = searchQuery.toLowerCase();
    return products.filter(product => 
      product.name.toLowerCase().includes(query) ||
      product.category.toLowerCase().includes(query) ||
      product.subcategory?.toLowerCase().includes(query) ||
      product.description.toLowerCase().includes(query)
    ).slice(0, 5); // Limit to 5 results for dropdown
  };

  const searchResults = getSearchResults();

  return (
    <nav className="bg-white shadow-lg sticky top-0 z-50">
      {/* Top announcement bar */}
      <div className="bg-gradient-to-r from-accent-500 to-accent-600 text-white text-center py-2 text-sm flex items-center justify-center gap-2">
        <Truck className="w-4 h-4" />
        <p>Free shipping on orders over PKR 3,000!</p>
      </div>

      {/* Main navbar */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center py-4">
          {/* Logo */}
          <div className="flex items-center">
            <Link to="/" className="text-2xl font-bold text-secondary-800">
              <span className="text-primary-600">Shop</span>
              <span className="text-accent-500">Hub</span>
            </Link>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-8">
            <Link to="/" className="text-secondary-700 hover:text-accent-500 transition duration-300">
              Home
            </Link>
            {categories.map((category) => (
              <div key={category.id} className="relative group">
                <div 
                  className="flex items-center cursor-pointer text-secondary-700 hover:text-accent-500 transition duration-300"
                  onClick={() => handleDropdownToggle(category.id)}
                >
                  <Link
                    to={`/category/${category.slug}`}
                    className="text-secondary-700 hover:text-accent-500 transition duration-300"
                    onClick={(e) => e.stopPropagation()}
                  >
                    {category.name}
                  </Link>
                  
                  {category.subcategories.length > 0 && (
                    <ChevronDown className="w-4 h-4 ml-1" />
                  )}
                </div>
                
                {/* Dropdown for subcategories */}
                {category.subcategories.length > 0 && (
                  <div 
                    className={`absolute left-0 mt-2 w-48 bg-white rounded-md shadow-lg z-10 transition-all duration-300 ${
                      activeDropdown === category.id ? 'opacity-100 visible' : 'opacity-0 invisible group-hover:opacity-100 group-hover:visible'
                    }`}
                  >
                    <div className="py-1">
                      {category.subcategories.map((subcategory, index) => (
                        <Link
                          key={index}
                          to={`/category/${category.slug}/${subcategory.toLowerCase()}`}
                          className="block px-4 py-2 text-sm text-secondary-700 hover:bg-primary-50 hover:text-accent-500"
                          onClick={() => setActiveDropdown(null)}
                        >
                          {subcategory}
                        </Link>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>

          {/* Search and Cart */}
          <div className="flex items-center space-x-4">
            {/* Advanced Search Bar */}
            <div className="hidden md:flex items-center relative">
              <AdvancedSearchBar className="w-64" />
            </div>

            {/* Wishlist Icon */}
            <Link to="/wishlist" className="relative p-2 text-secondary-700 hover:text-accent-500 transition duration-300">
              <Heart className="w-6 h-6" />
              {wishlistItems.length > 0 && (
                <span className="absolute -top-1 -right-1 bg-accent-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                  {wishlistItems.length}
                </span>
              )}
            </Link>

            {/* Cart Icon */}
            <Link to="/cart" className="relative p-2 text-secondary-700 hover:text-accent-500 transition duration-300">
              <ShoppingCart className="w-6 h-6" />
              {itemCount > 0 && (
                <span className="absolute -top-1 -right-1 bg-accent-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                  {itemCount}
                </span>
              )}
            </Link>

            {/* Mobile menu button */}
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="md:hidden p-2 text-secondary-700 hover:text-primary-500"
            >
              {isMenuOpen ? (
                <X className="w-6 h-6" />
              ) : (
                <Menu className="w-6 h-6" />
              )}
            </button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="md:hidden py-4 border-t border-secondary-200">
            <div className="flex flex-col space-y-2">
              <Link to="/" className="text-secondary-700 hover:text-accent-500 py-2">Home</Link>
              {categories.map((category) => (
                <div key={category.id}>
                  <div className="flex items-center justify-between">
                    <Link
                      to={`/category/${category.slug}`}
                      className="text-secondary-700 hover:text-accent-500 py-2 block"
                    >
                      {category.name}
                    </Link>
                    {category.subcategories.length > 0 && (
                      <button 
                        onClick={() => handleDropdownToggle(category.id)}
                        className="p-2 text-secondary-700"
                      >
                        <ChevronDown className={`w-4 h-4 transition-transform ${activeDropdown === category.id ? 'transform rotate-180' : ''}`} />
                      </button>
                    )}
                  </div>
                  {category.subcategories.length > 0 && activeDropdown === category.id && (
                    <div className="ml-4 space-y-1 mt-1">
                      {category.subcategories.map((subcategory, index) => (
                        <Link
                          key={index}
                          to={`/category/${category.slug}/${subcategory.toLowerCase()}`}
                          className="text-secondary-600 hover:text-accent-500 py-1 block text-sm"
                          onClick={() => {
                            setActiveDropdown(null);
                            setIsMenuOpen(false);
                          }}
                        >
                          {subcategory}
                        </Link>
                      ))}
                    </div>
                  )}
                </div>
              ))}
            </div>
            
            {/* Mobile Search */}
            <div className="mt-4">
              <form onSubmit={handleSearchSubmit} className="relative">
                <input
                  type="text"
                  value={searchQuery}
                  onChange={handleSearchChange}
                  placeholder="Search products..."
                  className="w-full px-4 py-2 border border-secondary-300 rounded-full focus:outline-none focus:ring-2 focus:ring-accent-500 focus:border-transparent"
                />
                <button 
                  type="submit"
                  className="absolute right-2 top-2 p-1 text-secondary-400 hover:text-accent-500"
                >
                  <Search className="w-5 h-5" />
                </button>
              </form>
              
              {/* Mobile Search History */}
              {searchHistory.length > 0 && (
                <div className="mt-3">
                  <div className="text-sm text-secondary-500 mb-2">Recent Searches:</div>
                  <div className="flex flex-wrap gap-2">
                    {searchHistory.slice(0, 3).map((query, index) => (
                      <button
                        key={index}
                        onClick={() => handleHistoryClick(query)}
                        className="px-3 py-1 bg-primary-100 text-secondary-700 rounded-full text-sm hover:bg-primary-200 transition-colors"
                      >
                        {query}
                      </button>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </nav>
  );
};

export default Navbar;
