import React, { useState, useEffect } from 'react';
import { X, Minus, Plus, ShoppingCart, Heart, Truck, CreditCard, Clock, Gift } from 'lucide-react';
import { useCart } from '../context/CartContext';
import { useWishlist } from '../context/WishlistContext';

const EnhancedCartDrawer = ({ isOpen, onClose }) => {
  const { 
    cartItems, 
    removeFromCart, 
    updateQuantity, 
    getCartTotal, 
    clearCart,
    addToCart 
  } = useCart();
  const { toggleWishlist, isInWishlist } = useWishlist();
  
  const [promoCode, setPromoCode] = useState('');
  const [appliedPromo, setAppliedPromo] = useState(null);
  const [showPromo, setShowPromo] = useState(false);
  const [checkoutStep, setCheckoutStep] = useState('cart');

  const { subtotal, tax, shipping, total, itemCount } = getCartTotal();

  // Promo codes
  const promoCodes = {
    'WELCOME10': { discount: 0.10, type: 'percentage', description: '10% off your first order' },
    'SAVE20': { discount: 0.20, type: 'percentage', description: '20% off orders over Rs. 5000' },
    'FREESHIP': { discount: 500, type: 'fixed', description: 'Free shipping on any order' },
    'NEWUSER': { discount: 0.15, type: 'percentage', description: '15% off for new users' }
  };

  const formatPrice = (price) => {
    return new Intl.NumberFormat('en-PK', {
      style: 'currency',
      currency: 'PKR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(price).replace('PKR', 'Rs.');
  };

  const applyPromoCode = () => {
    const promo = promoCodes[promoCode.toUpperCase()];
    if (promo) {
      setAppliedPromo({ code: promoCode.toUpperCase(), ...promo });
      setPromoCode('');
      setShowPromo(false);
    } else {
      alert('Invalid promo code');
    }
  };

  const removePromoCode = () => {
    setAppliedPromo(null);
  };

  const calculateDiscount = () => {
    if (!appliedPromo) return 0;
    
    if (appliedPromo.type === 'percentage') {
      return subtotal * appliedPromo.discount;
    } else {
      return appliedPromo.discount;
    }
  };

  const finalTotal = total - calculateDiscount();

  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }
    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex justify-end">
      <div className="bg-white w-full max-w-md h-full overflow-y-auto shadow-2xl">
        {/* Header */}
        <div className="sticky top-0 bg-white border-b border-primary-200 p-4 flex justify-between items-center z-10">
          <h2 className="text-xl font-bold text-secondary-800 flex items-center gap-2">
            <ShoppingCart className="w-6 h-6" />
            Shopping Cart ({itemCount})
          </h2>
          <button 
            onClick={onClose}
            className="p-2 hover:bg-primary-100 rounded-full transition-colors"
          >
            <X className="w-6 h-6 text-secondary-600" />
          </button>
        </div>

        {cartItems.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-96 p-8">
            <div className="w-32 h-32 bg-primary-100 rounded-full flex items-center justify-center mb-4">
              <ShoppingCart className="w-16 h-16 text-secondary-400" />
            </div>
            <h3 className="text-xl font-semibold text-secondary-800 mb-2">Your cart is empty</h3>
            <p className="text-secondary-600 text-center mb-6">
              Add some items to your cart to get started
            </p>
            <button 
              onClick={onClose}
              className="bg-gradient-to-r from-accent-500 to-accent-600 text-white px-6 py-3 rounded-lg font-semibold hover:from-accent-600 hover:to-accent-700 transition-all duration-200"
            >
              Continue Shopping
            </button>
          </div>
        ) : (
          <>
            {/* Cart Items */}
            <div className="p-4 space-y-4">
              {cartItems.map((item) => (
                <div key={`${item.id}-${item.selectedSize}-${item.selectedColor}`} 
                     className="flex items-center gap-4 p-4 bg-primary-50 rounded-lg">
                  <img
                    src={item.image}
                    alt={item.name}
                    className="w-16 h-16 object-cover rounded-lg"
                  />
                  <div className="flex-1">
                    <h3 className="font-semibold text-secondary-800">{item.name}</h3>
                    <p className="text-sm text-secondary-600">
                      {item.selectedSize && `Size: ${item.selectedSize}`}
                      {item.selectedSize && item.selectedColor && ' • '}
                      {item.selectedColor && `Color: ${item.selectedColor}`}
                    </p>
                    <div className="flex items-center justify-between mt-2">
                      <div className="flex items-center gap-2">
                        <button
                          onClick={() => updateQuantity(item.id, item.selectedSize, item.selectedColor, item.quantity - 1)}
                          className="p-1 hover:bg-primary-200 rounded-full transition-colors"
                        >
                          <Minus className="w-4 h-4" />
                        </button>
                        <span className="text-sm font-medium">{item.quantity}</span>
                        <button
                          onClick={() => updateQuantity(item.id, item.selectedSize, item.selectedColor, item.quantity + 1)}
                          className="p-1 hover:bg-primary-200 rounded-full transition-colors"
                        >
                          <Plus className="w-4 h-4" />
                        </button>
                      </div>
                      <div className="text-right">
                        <p className="font-semibold text-secondary-800">
                          {formatPrice(item.price * item.quantity)}
                        </p>
                        {item.originalPrice && item.originalPrice > item.price && (
                          <p className="text-sm text-secondary-500 line-through">
                            {formatPrice(item.originalPrice * item.quantity)}
                          </p>
                        )}
                      </div>
                    </div>
                  </div>
                  <div className="flex flex-col gap-2">
                    <button
                      onClick={() => toggleWishlist(item)}
                      className={`p-2 rounded-full transition-colors ${
                        isInWishlist(item.id) 
                          ? 'bg-accent-100 text-accent-600' 
                          : 'hover:bg-primary-200 text-secondary-400'
                      }`}
                    >
                      <Heart className="w-4 h-4" />
                    </button>
                    <button
                      onClick={() => removeFromCart(item.id, item.selectedSize, item.selectedColor)}
                      className="p-2 hover:bg-red-100 text-red-500 rounded-full transition-colors"
                    >
                      <X className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              ))}
            </div>

            {/* Promo Code Section */}
            <div className="p-4 border-t border-primary-200">
              {!showPromo ? (
                <button
                  onClick={() => setShowPromo(true)}
                  className="w-full text-left p-3 bg-primary-50 rounded-lg text-secondary-700 hover:bg-primary-100 transition-colors flex items-center gap-2"
                >
                  <Gift className="w-5 h-5" />
                  Have a promo code?
                </button>
              ) : (
                <div className="space-y-3">
                  <div className="flex gap-2">
                    <input
                      type="text"
                      value={promoCode}
                      onChange={(e) => setPromoCode(e.target.value)}
                      placeholder="Enter promo code"
                      className="flex-1 px-3 py-2 border border-primary-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-accent-400"
                    />
                    <button
                      onClick={applyPromoCode}
                      className="px-4 py-2 bg-accent-500 text-white rounded-lg hover:bg-accent-600 transition-colors"
                    >
                      Apply
                    </button>
                  </div>
                  {appliedPromo && (
                    <div className="flex items-center justify-between p-3 bg-green-50 border border-green-200 rounded-lg">
                      <div>
                        <p className="font-semibold text-green-800">{appliedPromo.code}</p>
                        <p className="text-sm text-green-600">{appliedPromo.description}</p>
                      </div>
                      <button
                        onClick={removePromoCode}
                        className="text-green-600 hover:text-green-800"
                      >
                        <X className="w-4 h-4" />
                      </button>
                    </div>
                  )}
                </div>
              )}
            </div>

            {/* Order Summary */}
            <div className="p-4 border-t border-primary-200 bg-primary-25">
              <h3 className="font-semibold text-secondary-800 mb-3">Order Summary</h3>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-secondary-600">Subtotal</span>
                  <span className="font-medium">{formatPrice(subtotal)}</span>
                </div>
                {appliedPromo && (
                  <div className="flex justify-between text-green-600">
                    <span>Discount ({appliedPromo.code})</span>
                    <span>-{formatPrice(calculateDiscount())}</span>
                  </div>
                )}
                <div className="flex justify-between">
                  <span className="text-secondary-600">Shipping</span>
                  <span className="font-medium">{shipping === 0 ? 'FREE' : formatPrice(shipping)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-secondary-600">Tax</span>
                  <span className="font-medium">{formatPrice(tax)}</span>
                </div>
                <div className="flex justify-between border-t border-primary-200 pt-2">
                  <span className="text-lg font-bold text-secondary-800">Total</span>
                  <span className="text-lg font-bold text-secondary-800">{formatPrice(finalTotal)}</span>
                </div>
              </div>
            </div>

            {/* Shipping Info */}
            <div className="p-4 bg-blue-50 border border-blue-200 mx-4 rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <Truck className="w-5 h-5 text-blue-600" />
                <span className="font-semibold text-blue-800">Free Shipping</span>
              </div>
              <p className="text-sm text-blue-700">
                Orders over Rs. 2,000 qualify for free shipping. 
                Estimated delivery: 3-5 business days.
              </p>
            </div>

            {/* Checkout Buttons */}
            <div className="p-4 space-y-3">
              <button className="w-full bg-gradient-to-r from-accent-500 to-accent-600 text-white py-3 px-6 rounded-lg hover:from-accent-600 hover:to-accent-700 transition-all duration-200 transform hover:scale-105 font-semibold shadow-lg flex items-center justify-center gap-2">
                <CreditCard className="w-5 h-5" />
                Checkout - {formatPrice(finalTotal)}
              </button>
              
              <button 
                onClick={onClose}
                className="w-full bg-white border border-primary-200 text-secondary-700 py-3 px-6 rounded-lg hover:bg-primary-50 transition-colors font-semibold"
              >
                Continue Shopping
              </button>
              
              <button 
                onClick={clearCart}
                className="w-full text-sm text-red-600 hover:text-red-700 py-2"
              >
                Clear Cart
              </button>
            </div>

            {/* Trust Badges */}
            <div className="p-4 border-t border-primary-200">
              <div className="flex justify-center gap-6 text-sm text-secondary-500">
                <div className="flex items-center gap-1">
                  <CreditCard className="w-4 h-4" />
                  <span>Secure Payment</span>
                </div>
                <div className="flex items-center gap-1">
                  <Truck className="w-4 h-4" />
                  <span>Fast Delivery</span>
                </div>
                <div className="flex items-center gap-1">
                  <Clock className="w-4 h-4" />
                  <span>24/7 Support</span>
                </div>
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default EnhancedCartDrawer;
