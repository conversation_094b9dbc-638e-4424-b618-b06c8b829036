import React from 'react';
import { Helmet } from 'react-helmet-async';

const SEOHead = ({ 
  title, 
  description, 
  keywords, 
  image, 
  url, 
  type = 'website',
  price,
  currency = 'PKR',
  availability = 'in stock',
  category,
  brand = 'ShopHub'
}) => {
  const siteTitle = 'ShopHub - Girls Fashion & Accessories';
  const fullTitle = title ? `${title} | ${siteTitle}` : siteTitle;
  const defaultDescription = "Shop the latest girls' fashion trends at ShopHub. Discover Eastern wear, Western outfits, and accessories with fast delivery across Pakistan.";
  const defaultKeywords = "girls fashion, Pakistani fashion, eastern wear, western wear, kurtis, dresses, accessories, online shopping Pakistan";
  
  const fullUrl = url ? `https://shophub.com${url}` : 'https://shophub.com';
  const defaultImage = image || 'https://shophub.com/images/og-image.jpg';
  
  return (
    <Helmet>
      {/* Basic Meta Tags */}
      <title>{fullTitle}</title>
      <meta name="description" content={description || defaultDescription} />
      <meta name="keywords" content={keywords || defaultKeywords} />
      <meta name="robots" content="index, follow" />
      <meta name="author" content="ShopHub" />
      <meta name="viewport" content="width=device-width, initial-scale=1.0" />
      
      {/* Open Graph Tags */}
      <meta property="og:title" content={fullTitle} />
      <meta property="og:description" content={description || defaultDescription} />
      <meta property="og:type" content={type} />
      <meta property="og:url" content={fullUrl} />
      <meta property="og:image" content={defaultImage} />
      <meta property="og:site_name" content="ShopHub" />
      <meta property="og:locale" content="en_US" />
      
      {/* Twitter Card Tags */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:title" content={fullTitle} />
      <meta name="twitter:description" content={description || defaultDescription} />
      <meta name="twitter:image" content={defaultImage} />
      <meta name="twitter:site" content="@ShopHub" />
      
      {/* Product-specific structured data */}
      {type === 'product' && price && (
        <script type="application/ld+json">
          {JSON.stringify({
            "@context": "https://schema.org/",
            "@type": "Product",
            "name": title,
            "image": [defaultImage],
            "description": description || defaultDescription,
            "brand": {
              "@type": "Brand",
              "name": brand
            },
            "offers": {
              "@type": "Offer",
              "url": fullUrl,
              "priceCurrency": currency,
              "price": price,
              "priceValidUntil": new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
              "availability": `https://schema.org/${availability.replace(' ', '')}`
            },
            "category": category
          })}
        </script>
      )}
      
      {/* Organization structured data */}
      <script type="application/ld+json">
        {JSON.stringify({
          "@context": "https://schema.org",
          "@type": "Organization",
          "name": "ShopHub",
          "url": "https://shophub.com",
          "logo": "https://shophub.com/logo.png",
          "sameAs": [
            "https://www.facebook.com/shophub",
            "https://www.instagram.com/shophub",
            "https://www.twitter.com/shophub"
          ],
          "contactPoint": {
            "@type": "ContactPoint",
            "telephone": "+92-300-1234567",
            "contactType": "customer service"
          }
        })}
      </script>
      
      {/* Canonical URL */}
      <link rel="canonical" href={fullUrl} />
      
      {/* Favicon */}
      <link rel="icon" type="image/png" href="/favicon.png" />
      <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
    </Helmet>
  );
};

// Google Analytics Component
export const GoogleAnalytics = ({ trackingId }) => {
  if (!trackingId) return null;
  
  return (
    <Helmet>
      <script async src={`https://www.googletagmanager.com/gtag/js?id=${trackingId}`} />
      <script>
        {`
          window.dataLayer = window.dataLayer || [];
          function gtag(){dataLayer.push(arguments);}
          gtag('js', new Date());
          gtag('config', '${trackingId}');
        `}
      </script>
    </Helmet>
  );
};

// Facebook Pixel Component
export const FacebookPixel = ({ pixelId }) => {
  if (!pixelId) return null;
  
  return (
    <Helmet>
      <script>
        {`
          !function(f,b,e,v,n,t,s)
          {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
          n.callMethod.apply(n,arguments):n.queue.push(arguments)};
          if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
          n.queue=[];t=b.createElement(e);t.async=!0;
          t.src=v;s=b.getElementsByTagName(e)[0];
          s.parentNode.insertBefore(t,s)}(window, document,'script',
          'https://connect.facebook.net/en_US/fbevents.js');
          fbq('init', '${pixelId}');
          fbq('track', 'PageView');
        `}
      </script>
      <noscript>
        <img 
          height="1" 
          width="1" 
          style={{ display: 'none' }}
          src={`https://www.facebook.com/tr?id=${pixelId}&ev=PageView&noscript=1`}
          alt=""
        />
      </noscript>
    </Helmet>
  );
};

// Analytics Events Helper
export const trackEvent = (eventName, eventData = {}) => {
  // Google Analytics
  if (window.gtag) {
    window.gtag('event', eventName, eventData);
  }
  
  // Facebook Pixel
  if (window.fbq) {
    window.fbq('track', eventName, eventData);
  }
  
  // Custom analytics
  if (window.analytics) {
    window.analytics.track(eventName, eventData);
  }
};

// E-commerce tracking events
export const trackPurchase = (transactionId, items, total, currency = 'PKR') => {
  const eventData = {
    transaction_id: transactionId,
    value: total,
    currency: currency,
    items: items.map(item => ({
      item_id: item.id,
      item_name: item.name,
      category: item.category,
      quantity: item.quantity,
      price: item.price
    }))
  };
  
  trackEvent('purchase', eventData);
};

export const trackAddToCart = (item, quantity = 1) => {
  const eventData = {
    currency: 'PKR',
    value: item.price * quantity,
    items: [{
      item_id: item.id,
      item_name: item.name,
      category: item.category,
      quantity: quantity,
      price: item.price
    }]
  };
  
  trackEvent('add_to_cart', eventData);
};

export const trackViewItem = (item) => {
  const eventData = {
    currency: 'PKR',
    value: item.price,
    items: [{
      item_id: item.id,
      item_name: item.name,
      category: item.category,
      price: item.price
    }]
  };
  
  trackEvent('view_item', eventData);
};

export const trackSearch = (searchTerm, resultsCount) => {
  trackEvent('search', {
    search_term: searchTerm,
    results_count: resultsCount
  });
};

export const trackBeginCheckout = (items, total) => {
  const eventData = {
    currency: 'PKR',
    value: total,
    items: items.map(item => ({
      item_id: item.id,
      item_name: item.name,
      category: item.category,
      quantity: item.quantity,
      price: item.price
    }))
  };
  
  trackEvent('begin_checkout', eventData);
};

// Newsletter signup component
export const NewsletterSignup = ({ className = "" }) => {
  const [email, setEmail] = React.useState('');
  const [isLoading, setIsLoading] = React.useState(false);
  const [isSuccess, setIsSuccess] = React.useState(false);
  
  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsLoading(true);
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Track newsletter signup
      trackEvent('newsletter_signup', { email });
      
      setIsSuccess(true);
      setEmail('');
    } catch (error) {
      console.error('Newsletter signup error:', error);
    } finally {
      setIsLoading(false);
    }
  };
  
  return (
    <div className={`bg-gradient-to-r from-accent-500 to-accent-600 rounded-lg p-6 text-white ${className}`}>
      <h3 className="text-lg font-semibold mb-2">Stay Updated!</h3>
      <p className="text-accent-100 mb-4">
        Get the latest fashion trends and exclusive offers delivered to your inbox.
      </p>
      
      {isSuccess ? (
        <div className="text-center py-4">
          <p className="text-accent-100">Thanks for subscribing! 🎉</p>
        </div>
      ) : (
        <form onSubmit={handleSubmit} className="flex gap-2">
          <input
            type="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            placeholder="Enter your email"
            required
            className="flex-1 px-4 py-2 rounded-lg bg-white/20 backdrop-blur-sm border border-white/30 text-white placeholder-white/70 focus:outline-none focus:ring-2 focus:ring-white/50"
          />
          <button
            type="submit"
            disabled={isLoading}
            className="px-6 py-2 bg-white text-accent-600 rounded-lg font-semibold hover:bg-accent-50 transition-colors disabled:opacity-50"
          >
            {isLoading ? 'Subscribing...' : 'Subscribe'}
          </button>
        </form>
      )}
    </div>
  );
};

export default SEOHead;
