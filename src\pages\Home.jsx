import React from 'react';
import { Link } from 'react-router-dom';
import Banner from '../components/Banner';
import ProductCard from '../components/ProductCard';
import products from '../data/products';
import { Heart, Instagram } from 'lucide-react';

const Home = () => {
  // Featured products (first 6 products)
  const featuredProducts = products.slice(0, 6);
  
  // Sale products
  const saleProducts = products.filter(product => product.isOnSale);
  
  // New arrivals
  const newArrivals = products.filter(product => product.isNew);

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary-50 via-primary-100/50 to-primary-200/30">
      {/* Hero Banner */}
      <Banner />

      {/* Featured Products */}
      <section className="py-16 bg-gradient-to-r from-white via-primary-50/30 to-primary-100/30">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-secondary-800 mb-4">
              Featured Products
            </h2>
            <p className="text-lg text-secondary-600 max-w-2xl mx-auto">
              Discover our handpicked selection of the most popular items loved by our customers
            </p>
          </div>
          
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-2 xl:grid-cols-3 gap-10">
            {featuredProducts.map((product) => (
              <ProductCard key={product.id} product={product} />
            ))}
          </div>
          
          <div className="text-center mt-12">
            <Link 
              to="/category/all" 
              className="bg-gradient-to-r from-accent-500 to-accent-600 text-white px-8 py-3 rounded-full font-semibold hover:from-accent-600 hover:to-accent-700 transition-all duration-200 transform hover:scale-105 inline-block shadow-lg"
            >
              View All Products
            </Link>
          </div>
        </div>
      </section>

      {/* New Arrivals Section */}
      <section className="py-16 bg-gradient-to-br from-secondary-50 via-secondary-100/50 to-secondary-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center mb-12">
            <div>
              <h2 className="text-3xl md:text-4xl font-bold text-secondary-800 mb-2">
                New Arrivals
              </h2>
              <p className="text-lg text-secondary-600">
                Fresh styles just in - be the first to wear them!
              </p>
            </div>
            <Link 
              to="/category/new-arrivals" 
              className="hidden md:block text-accent-600 hover:text-accent-700 font-semibold"
            >
              View All →
            </Link>
          </div>
          
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-10">
            {newArrivals.slice(0, 3).map((product) => (
              <ProductCard key={product.id} product={product} />
            ))}
          </div>
          
          <div className="text-center mt-8 md:hidden">
            <Link 
              to="/category/new-arrivals" 
              className="text-accent-600 hover:text-accent-700 font-semibold"
            >
              View All New Arrivals →
            </Link>
          </div>
        </div>
      </section>

      {/* Sale Section */}
      <section className="py-16 bg-gradient-to-r from-primary-50 via-accent-50 to-primary-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-secondary-800 mb-4">
              🔥 Hot Sale Items
            </h2>
            <p className="text-secondary-600 text-lg">
              Limited time offers - Grab them before they're gone!
            </p>
          </div>
          
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-10">
            {saleProducts.slice(0, 6).map((product) => (
              <ProductCard key={product.id} product={product} />
            ))}
          </div>
        </div>
      </section>

      {/* Categories Showcase */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-800 mb-4">
              Shop by Style
            </h2>
            <p className="text-lg text-gray-600">
              Find your perfect look from our diverse collection
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {/* Eastern Wear */}
            <Link to="/category/eastern" className="relative group cursor-pointer">
              <div className="bg-gradient-to-br from-pink-400 to-pink-600 rounded-2xl p-8 text-white overflow-hidden">
                <div className="relative z-10">
                  <h3 className="text-2xl md:text-3xl font-bold mb-4">
                    Eastern Collection
                  </h3>
                  <p className="text-pink-100 mb-6 text-lg">
                    Elegant kurtis, stunning suits, and beautiful sarees
                  </p>
                  <span className="bg-white text-pink-600 px-6 py-3 rounded-full font-semibold hover:bg-pink-50 transition-colors duration-200 inline-block">
                    Explore Eastern →
                  </span>
                </div>
                <div className="absolute -right-4 -bottom-4 w-32 h-32 bg-white bg-opacity-20 rounded-full"></div>
                <div className="absolute -right-8 -top-8 w-24 h-24 bg-white bg-opacity-10 rounded-full"></div>
              </div>
            </Link>

            {/* Western Wear */}
            <Link to="/category/western" className="relative group cursor-pointer">
              <div className="bg-gradient-to-br from-purple-400 to-purple-600 rounded-2xl p-8 text-white overflow-hidden">
                <div className="relative z-10">
                  <h3 className="text-2xl md:text-3xl font-bold mb-4">
                    Western Collection
                  </h3>
                  <p className="text-purple-100 mb-6 text-lg">
                    Trendy dresses, chic tops, and stylish jeans
                  </p>
                  <span className="bg-white text-purple-600 px-6 py-3 rounded-full font-semibold hover:bg-purple-50 transition-colors duration-200 inline-block">
                    Explore Western →
                  </span>
                </div>
                <div className="absolute -right-4 -bottom-4 w-32 h-32 bg-white bg-opacity-20 rounded-full"></div>
                <div className="absolute -right-8 -top-8 w-24 h-24 bg-white bg-opacity-10 rounded-full"></div>
              </div>
            </Link>
          </div>
        </div>
      </section>

      {/* Newsletter Section */}
      <section className="py-16 bg-gray-800 text-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold mb-4">
            Stay In Style
          </h2>
          <p className="text-xl text-gray-300 mb-8">
            Get the latest fashion trends, exclusive deals, and style tips delivered to your inbox
          </p>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center max-w-md mx-auto">
            <input
              type="email"
              placeholder="Enter your email address"
              className="flex-1 px-6 py-3 rounded-full text-gray-800 focus:outline-none focus:ring-2 focus:ring-pink-500"
            />
            <button className="bg-gradient-to-r from-pink-500 to-purple-500 text-white px-8 py-3 rounded-full font-semibold hover:from-pink-600 hover:to-purple-600 transition-all duration-200 transform hover:scale-105">
              Subscribe
            </button>
          </div>
          
          <p className="text-sm text-gray-400 mt-4">
            Join 10,000+ fashion enthusiasts and never miss a trend!
          </p>
        </div>
      </section>

      {/* Instagram Feed Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-800 mb-4">
              #ShopHubStyle
            </h2>
            <p className="text-gray-600 text-lg">
              See how our customers are styling their ShopHub pieces
            </p>
          </div>
          
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {[
              "https://images.unsplash.com/photo-1490481651871-ab68de25d43d?auto=format&fit=crop&w=300&h=300&q=80",
              "https://images.unsplash.com/photo-1515372039744-b8f02a3ae446?auto=format&fit=crop&w=300&h=300&q=80",
              "https://images.unsplash.com/photo-1551232864-3f0890e580d9?auto=format&fit=crop&w=300&h=300&q=80",
              "https://images.unsplash.com/photo-1469334031218-e382a71b716b?auto=format&fit=crop&w=300&h=300&q=80",
              "https://images.unsplash.com/photo-1544022613-e87ca75a784a?auto=format&fit=crop&w=300&h=300&q=80",
              "https://images.unsplash.com/photo-1596783074918-c84cb06531ca?auto=format&fit=crop&w=300&h=300&q=80",
              "https://images.unsplash.com/photo-1487412720507-e7ab37603c6f?auto=format&fit=crop&w=300&h=300&q=80",
              "https://images.unsplash.com/photo-1524504388940-b1c1722653e1?auto=format&fit=crop&w=300&h=300&q=80"
            ].map((imageUrl, i) => (
              <div key={i} className="aspect-square bg-gray-200 rounded-lg overflow-hidden hover:opacity-75 transition-opacity duration-200 cursor-pointer group relative">
                <img
                  src={imageUrl}
                  alt={`Customer Style ${i + 1}`}
                  className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-300"
                  onError={(e) => {
                    e.target.src = `https://picsum.photos/300/300?random=${i + 1}`;
                  }}
                />
                <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300 flex items-center justify-center">
                  <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                    <Heart className="w-8 h-8 text-white" />
                  </div>
                </div>
              </div>
            ))}
          </div>
          
          <div className="text-center mt-8">
            <a 
              href="https://instagram.com/shophub" 
              target="_blank" 
              rel="noopener noreferrer"
              className="border-2 border-pink-500 text-pink-500 px-8 py-3 rounded-full font-semibold hover:bg-pink-50 transition-colors duration-200 inline-flex items-center gap-2"
            >
              <Instagram className="w-5 h-5" />
              Follow @ShopHub
            </a>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Home;
