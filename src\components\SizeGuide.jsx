import React, { useState } from 'react';
import { X, Ruler, User, Info } from 'lucide-react';

const SizeGuide = ({ isOpen, onClose, category = 'clothing' }) => {
  const [activeTab, setActiveTab] = useState('sizes');

  const sizingData = {
    clothing: {
      sizes: {
        'XS': { bust: '30-32', waist: '24-26', hips: '34-36' },
        'S': { bust: '32-34', waist: '26-28', hips: '36-38' },
        'M': { bust: '34-36', waist: '28-30', hips: '38-40' },
        'L': { bust: '36-38', waist: '30-32', hips: '40-42' },
        'XL': { bust: '38-40', waist: '32-34', hips: '42-44' },
        'XXL': { bust: '40-42', waist: '34-36', hips: '44-46' }
      },
      measurements: [
        { name: 'Bus<PERSON>', instruction: 'Measure around the fullest part of your chest' },
        { name: '<PERSON>aist', instruction: 'Measure around the narrowest part of your waist' },
        { name: 'Hips', instruction: 'Measure around the fullest part of your hips' }
      ]
    },
    footwear: {
      sizes: {
        '36': { length: '23', width: '8.5' },
        '37': { length: '23.5', width: '9' },
        '38': { length: '24', width: '9.5' },
        '39': { length: '24.5', width: '10' },
        '40': { length: '25', width: '10.5' },
        '41': { length: '25.5', width: '11' },
        '42': { length: '26', width: '11.5' }
      },
      measurements: [
        { name: 'Length', instruction: 'Measure from heel to longest toe' },
        { name: 'Width', instruction: 'Measure the widest part of your foot' }
      ]
    }
  };

  const currentData = sizingData[category] || sizingData.clothing;

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4 backdrop-blur-sm">
      <div className="bg-white rounded-2xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="sticky top-0 bg-white border-b border-primary-200 p-6 flex justify-between items-center">
          <h2 className="text-2xl font-bold text-secondary-800 flex items-center gap-2">
            <Ruler className="w-6 h-6" />
            Size Guide
          </h2>
          <button 
            onClick={onClose}
            className="p-2 hover:bg-primary-100 rounded-full transition-colors"
          >
            <X className="w-6 h-6 text-secondary-600" />
          </button>
        </div>

        {/* Tabs */}
        <div className="border-b border-primary-200">
          <div className="flex px-6">
            <button
              onClick={() => setActiveTab('sizes')}
              className={`py-3 px-4 text-sm font-medium border-b-2 transition-colors ${
                activeTab === 'sizes'
                  ? 'border-accent-500 text-accent-600'
                  : 'border-transparent text-secondary-600 hover:text-secondary-800'
              }`}
            >
              Size Chart
            </button>
            <button
              onClick={() => setActiveTab('measurements')}
              className={`py-3 px-4 text-sm font-medium border-b-2 transition-colors ${
                activeTab === 'measurements'
                  ? 'border-accent-500 text-accent-600'
                  : 'border-transparent text-secondary-600 hover:text-secondary-800'
              }`}
            >
              How to Measure
            </button>
          </div>
        </div>

        <div className="p-6">
          {activeTab === 'sizes' && (
            <div className="space-y-6">
              <div className="bg-accent-50 border border-accent-200 rounded-lg p-4">
                <p className="text-sm text-accent-700 flex items-center gap-2">
                  <Info className="w-4 h-4" />
                  All measurements are in inches. For the best fit, measure yourself and compare with our size chart.
                </p>
              </div>

              <div className="overflow-x-auto">
                <table className="w-full border-collapse border border-primary-200">
                  <thead>
                    <tr className="bg-primary-50">
                      <th className="border border-primary-200 px-4 py-3 text-left text-sm font-semibold text-secondary-800">
                        Size
                      </th>
                      {Object.keys(currentData.sizes[Object.keys(currentData.sizes)[0]]).map(measurement => (
                        <th key={measurement} className="border border-primary-200 px-4 py-3 text-left text-sm font-semibold text-secondary-800 capitalize">
                          {measurement}
                        </th>
                      ))}
                    </tr>
                  </thead>
                  <tbody>
                    {Object.entries(currentData.sizes).map(([size, measurements]) => (
                      <tr key={size} className="hover:bg-primary-25">
                        <td className="border border-primary-200 px-4 py-3 font-medium text-secondary-800">
                          {size}
                        </td>
                        {Object.values(measurements).map((measurement, index) => (
                          <td key={index} className="border border-primary-200 px-4 py-3 text-secondary-700">
                            {measurement}"
                          </td>
                        ))}
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}

          {activeTab === 'measurements' && (
            <div className="space-y-6">
              <div className="bg-primary-50 border border-primary-200 rounded-lg p-4">
                <p className="text-sm text-secondary-700 flex items-center gap-2">
                  <User className="w-4 h-4" />
                  For accurate measurements, have someone help you or use a mirror.
                </p>
              </div>

              <div className="space-y-4">
                {currentData.measurements.map((measurement, index) => (
                  <div key={index} className="bg-white border border-primary-200 rounded-lg p-4">
                    <h3 className="font-semibold text-secondary-800 mb-2">{measurement.name}</h3>
                    <p className="text-sm text-secondary-600">{measurement.instruction}</p>
                  </div>
                ))}
              </div>

              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                <h3 className="font-semibold text-yellow-800 mb-2">Tips for Accurate Measurement:</h3>
                <ul className="text-sm text-yellow-700 space-y-1">
                  <li>• Use a flexible measuring tape</li>
                  <li>• Keep the tape snug but not tight</li>
                  <li>• Measure over your undergarments</li>
                  <li>• Stand up straight and breathe normally</li>
                  <li>• Take measurements at the same time of day</li>
                </ul>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default SizeGuide;
